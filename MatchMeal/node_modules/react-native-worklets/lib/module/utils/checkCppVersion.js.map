{"version": 3, "names": ["logger", "WorkletsError", "jsVersion", "checkCppVersion", "cppVersion", "global", "_WORKLETS_VERSION_CPP", "undefined", "warn", "ok", "matchVersion", "version1", "version2", "match", "major1", "minor1", "split", "major2", "minor2"], "sourceRoot": "../../../src", "sources": ["utils/checkCppVersion.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,MAAM,QAAQ,cAAW;AAClC,SAASC,aAAa,QAAQ,qBAAkB;AAChD,SAASC,SAAS,QAAQ,gBAAa;AAEvC,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMC,UAAU,GAAGC,MAAM,CAACC,qBAAqB;EAC/C,IAAIF,UAAU,KAAKG,SAAS,EAAE;IAC5BP,MAAM,CAACQ,IAAI,CACT;AACN,wKACI,CAAC;IACD;EACF;EACA,MAAMC,EAAE,GAAGC,YAAY,CAACR,SAAS,EAAEE,UAAU,CAAC;EAC9C,IAAI,CAACK,EAAE,EAAE;IACP,MAAM,IAAIR,aAAa,CACrB,iEAAiEC,SAAS,OAAOE,UAAU;AACjG,wKACI,CAAC;EACH;AACF;AAEA,OAAO,SAASM,YAAYA,CAACC,QAAgB,EAAEC,QAAgB,EAAE;EAC/D,IAAID,QAAQ,CAACE,KAAK,CAAC,iBAAiB,CAAC,IAAID,QAAQ,CAACC,KAAK,CAAC,iBAAiB,CAAC,EAAE;IAC1E;IACA,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGJ,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;IAC5C,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGN,QAAQ,CAACI,KAAK,CAAC,GAAG,CAAC;IAC5C,OAAOF,MAAM,KAAKG,MAAM,IAAIF,MAAM,KAAKG,MAAM;EAC/C,CAAC,MAAM;IACL;IACA,OAAOP,QAAQ,KAAKC,QAAQ;EAC9B;AACF", "ignoreList": []}