{"version": 3, "names": ["SHOULD_BE_USE_WEB", "serializableMappingFlag", "Symbol", "cache", "WeakMap", "serializableMappingCache", "set", "get", "serializable", "serializableRef", "bind"], "sourceRoot": "../../src", "sources": ["serializableMappingCache.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iBAAiB,QAAQ,4BAAmB;AAGrD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGC,MAAM,CAAC,mBAAmB,CAAC;;AAElE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,KAAK,GAAGH,iBAAiB,GAC3B,IAAI,GACJ,IAAII,OAAO,CAAmC,CAAC;AAEnD,OAAO,MAAMC,wBAAwB,GAAGL,iBAAiB,GACrD;EACEM,GAAGA,CAAA,EAAG;IACJ;EAAA,CACD;EACDC,GAAGA,CAAA,EAAG;IACJ,OAAO,IAAI;EACb;AACF,CAAC,GACD;EACED,GAAGA,CAACE,YAAoB,EAAEC,eAAiC,EAAQ;IACjEN,KAAK,CAAEG,GAAG,CAACE,YAAY,EAAEC,eAAe,IAAIR,uBAAuB,CAAC;EACtE,CAAC;EACDM,GAAG,EAAEJ,KAAK,CAAEI,GAAG,CAACG,IAAI,CAACP,KAAK;AAC5B,CAAC", "ignoreList": []}