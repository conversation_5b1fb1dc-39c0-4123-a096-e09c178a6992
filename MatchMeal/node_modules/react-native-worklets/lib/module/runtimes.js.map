{"version": 3, "names": ["setupCallGuard", "getMemorySafeCapturableConsole", "setupConsole", "SHOULD_BE_USE_WEB", "setupRunLoop", "RuntimeKind", "createSerializable", "makeShareableCloneOnUIRecursive", "isWorkletFunction", "registerWorkletsError", "WorkletsError", "WorkletsModule", "createWorkletRuntime", "nameOrConfig", "initializer", "runtimeBoundCapturableConsole", "name", "initializerFn", "useDefaultQueue", "customQueue", "animationQueuePollingRate", "enableEventLoop", "Math", "round", "runOnRuntime", "workletRuntime", "worklet", "__DEV__", "globalThis", "__RUNTIME_KIND", "ReactNative", "args", "_scheduleOnRuntime", "scheduleOnRuntime", "__flushMicrotasks"], "sourceRoot": "../../src", "sources": ["runtimes.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,gBAAa;AAC5C,SAASC,8BAA8B,EAAEC,YAAY,QAAQ,mBAAgB;AAC7E,SAASC,iBAAiB,QAAQ,4BAAmB;AACrD,SAASC,YAAY,QAAQ,mCAA0B;AACvD,SAASC,WAAW,QAAQ,kBAAe;AAC3C,SACEC,kBAAkB,EAClBC,+BAA+B,QAC1B,mBAAgB;AACvB,SAASC,iBAAiB,QAAQ,sBAAmB;AACrD,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,oBAAiB;AACtE,SAASC,cAAc,QAAQ,2BAAkB;;AAGjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,OAAO,SAASC,oBAAoBA,CAClCC,YAAoD,EACpDC,WAAuC,EACvB;EAChB,MAAMC,6BAA6B,GAAGd,8BAA8B,CAAC,CAAC;EAEtE,IAAIe,IAAY;EAChB,IAAIC,aAAuC;EAC3C,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIC,WAA+B;EACnC,IAAIC,yBAAiC;EACrC,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAI,OAAOR,YAAY,KAAK,QAAQ,EAAE;IACpCG,IAAI,GAAGH,YAAY;IACnBI,aAAa,GAAGH,WAAW;EAC7B,CAAC,MAAM;IACL;IACAE,IAAI,GAAGH,YAAY,EAAEG,IAAI,IAAI,WAAW;IACxCC,aAAa,GAAGJ,YAAY,EAAEC,WAAW;IACzCI,eAAe,GAAGL,YAAY,EAAEK,eAAe,IAAI,IAAI;IACvDC,WAAW,GAAGN,YAAY,EAAEM,WAAW;IACvCC,yBAAyB,GAAGE,IAAI,CAACC,KAAK,CACpCV,YAAY,EAAEO,yBAAyB,IAAI,EAC7C,CAAC;IACDC,eAAe,GAAGR,YAAY,EAAEQ,eAAe,IAAI,IAAI;EACzD;EAEA,IAAIJ,aAAa,IAAI,CAACT,iBAAiB,CAACS,aAAa,CAAC,EAAE;IACtD,MAAM,IAAIP,aAAa,CACrB,oEACF,CAAC;EACH;EAEA,OAAOC,cAAc,CAACC,oBAAoB,CACxCI,IAAI,EACJV,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTN,cAAc,CAAC,CAAC;IAChBS,qBAAqB,CAAC,CAAC;IACvBP,YAAY,CAACa,6BAA6B,CAAC;IAC3C,IAAIM,eAAe,EAAE;MACnBjB,YAAY,CAACgB,yBAAyB,CAAC;IACzC;IACAH,aAAa,GAAG,CAAC;EACnB,CAAC,CAAC,EACFC,eAAe,EACfC,WAAW,EACXE,eACF,CAAC;AACH;;AAEA;;AAKA;AACA,OAAO,SAASG,YAAYA,CAC1BC,cAA8B,EAC9BC,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACxB,iBAAiB,IAAI,CAACK,iBAAiB,CAACkB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIhB,aAAa,CACrB,yDACF,CAAC;EACH;EACA,IAAIkB,UAAU,CAACC,cAAc,KAAKxB,WAAW,CAACyB,WAAW,EAAE;IACzD,OAAO,CAAC,GAAGC,IAAI,KACbH,UAAU,CAACI,kBAAkB,CAC3BP,cAAc,EACdlB,+BAA+B,CAAC,MAAM;MACpC,SAAS;;MACTmB,OAAO,CAAC,GAAGK,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EACL;EACA,OAAO,CAAC,GAAGA,IAAI,KACbpB,cAAc,CAACsB,iBAAiB,CAC9BR,cAAc,EACdnB,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACToB,OAAO,CAAC,GAAGK,IAAI,CAAC;IAChBH,UAAU,CAACM,iBAAiB,CAAC,CAAC;EAChC,CAAC,CACH,CAAC;AACL;;AAEA", "ignoreList": []}