{"version": 3, "names": ["init", "SHOULD_BE_USE_WEB", "RuntimeKind", "WorkletsError", "bundleModeInit", "globalThis", "_WORKLETS_BUNDLE_MODE", "runtimeKind", "__RUNTIME_KIND", "ReactNative"], "sourceRoot": "../../src", "sources": ["workletRuntimeEntry.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,IAAI,QAAQ,mBAAgB;AACrC,SAASC,iBAAiB,QAAQ,4BAAmB;AACrD,SAASC,WAAW,QAAQ,kBAAe;AAC3C,SAASC,aAAa,QAAQ,oBAAiB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,IAAIH,iBAAiB,EAAE;IACrB;EACF;EAEAI,UAAU,CAACC,qBAAqB,GAAG,IAAI;EAEvC,MAAMC,WAAW,GAAGF,UAAU,CAACG,cAAc;EAC7C,IAAID,WAAW,IAAIA,WAAW,KAAKL,WAAW,CAACO,WAAW,EAAE;IAC1D;AACJ;AACA;AACA;IACIT,IAAI,CAAC,CAAC;IACN,MAAM,IAAIG,aAAa,CAAC,mCAAmC,CAAC;EAC9D;AACF;AAEAC,cAAc,CAAC,CAAC", "ignoreList": []}