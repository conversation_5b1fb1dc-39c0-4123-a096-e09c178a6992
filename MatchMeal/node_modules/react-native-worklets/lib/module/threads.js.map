{"version": 3, "names": ["IS_JEST", "SHOULD_BE_USE_WEB", "RuntimeKind", "createSerializable", "makeShareableCloneOnUIRecursive", "serializableMappingCache", "isWorkletFunction", "WorkletsError", "WorkletsModule", "runOnUIQueue", "setupMicrotasks", "microtasksQueue", "isExecutingMicrotasksQueue", "global", "queueMicrotask", "callback", "push", "_microtaskQueueFinalizers", "__callMicrotasks", "index", "length", "for<PERSON>ach", "finalizer", "callMicrotasksOnUIThread", "callMicrotasks", "scheduleOnUI", "worklet", "args", "runOnUI", "__DEV__", "__bundleData", "enqueueUI", "runOnUIWorklet", "serializableRunOnUIWorklet", "set", "runOnUISync", "executeOnUIRuntimeSync", "result", "runWorkletOnJS", "runOnJS", "fun", "globalThis", "__RUNTIME_KIND", "ReactNative", "__remoteFunction", "scheduleOnJS", "_scheduleHostFunctionOnJS", "_scheduleRemoteFunctionOnJS", "undefined", "scheduleOnRN", "runOnUIAsync", "Promise", "resolve", "runOnUIAsyncWorklet", "serializableRunOnUIAsyncWorklet", "job", "flushUIQueue", "queue", "workletFunction", "workletArgs", "jobResolve", "unstable_eventLoopTask"], "sourceRoot": "../../src", "sources": ["threads.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,OAAO,EAAEC,iBAAiB,QAAQ,4BAAmB;AAC9D,SAASC,WAAW,QAAQ,kBAAe;AAC3C,SACEC,kBAAkB,EAClBC,+BAA+B,QAC1B,mBAAgB;AACvB,SAASC,wBAAwB,QAAQ,+BAA4B;AACrE,SAASC,iBAAiB,QAAQ,sBAAmB;AACrD,SAASC,aAAa,QAAQ,oBAAiB;AAC/C,SAASC,cAAc,QAAQ,2BAAkB;AASjD,IAAIC,YAAqB,GAAG,EAAE;AAE9B,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,SAAS;;EAET,IAAIC,eAAkC,GAAG,EAAE;EAC3C,IAAIC,0BAA0B,GAAG,KAAK;EACtCC,MAAM,CAACC,cAAc,GAAIC,QAAoB,IAAK;IAChDJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC;EAChC,CAAC;EACDF,MAAM,CAACI,yBAAyB,GAAG,EAAE;EAErCJ,MAAM,CAACK,gBAAgB,GAAG,MAAM;IAC9B,IAAIN,0BAA0B,EAAE;MAC9B;IACF;IACA,IAAI;MACFA,0BAA0B,GAAG,IAAI;MACjC,KAAK,IAAIO,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGR,eAAe,CAACS,MAAM,EAAED,KAAK,IAAI,CAAC,EAAE;QAC9D;QACAR,eAAe,CAACQ,KAAK,CAAC,CAAC,CAAC;MAC1B;MACAR,eAAe,GAAG,EAAE;MACpBE,MAAM,CAACI,yBAAyB,CAACI,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAAC,CAAC,CAAC;IACtE,CAAC,SAAS;MACRV,0BAA0B,GAAG,KAAK;IACpC;EACF,CAAC;AACH;AAEA,SAASW,wBAAwBA,CAAA,EAAG;EAClC,SAAS;;EACTV,MAAM,CAACK,gBAAgB,CAAC,CAAC;AAC3B;AAEA,OAAO,MAAMM,cAAc,GAAGvB,iBAAiB,GAC3C,MAAM;EACJ;AAAA,CACD,GACDsB,wBAAwB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAYA,CAC1BC,OAAuC,EACvC,GAAGC,IAAU,EACP;EACNC,OAAO,CAACF,OAAO,CAAC,CAAC,GAAGC,IAAI,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,OAAO,SAASC,OAAOA,CACrBF,OAA2C,EAClB;EACzB,IACEG,OAAO,IACP,CAAC5B,iBAAiB,IAClB,CAACK,iBAAiB,CAACoB,OAAO,CAAC,IAC3B,CAAEA,OAAO,CAA8BI,YAAY,EACnD;IACA,MAAM,IAAIvB,aAAa,CAAC,2CAA2C,CAAC;EACtE;EACA,OAAO,CAAC,GAAGoB,IAAI,KAAK;IAClB,IAAI3B,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAQ,cAAc,CAACiB,YAAY,CACzBtB,kBAAkB,CAAC,MAAM;QACvB,SAAS;;QACTuB,OAAO,CAAC,GAAGC,IAAI,CAAC;MAClB,CAAC,CACH,CAAC;MACD;IACF;IACA,IAAIE,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACA1B,kBAAkB,CAACuB,OAAO,CAAC;MAC3BvB,kBAAkB,CAACwB,IAAI,CAAC;IAC1B;IAEAI,SAAS,CAACL,OAAO,EAAEC,IAAI,CAAC;EAC1B,CAAC;AACH;AAEA,IAAIE,OAAO,EAAE;EACX,SAASG,cAAcA,CAAA,EAAS;IAC9B,SAAS;;IACT,MAAM,IAAIzB,aAAa,CACrB,kJACF,CAAC;EACH;EAEA,MAAM0B,0BAA0B,GAAG9B,kBAAkB,CAAC6B,cAAc,CAAC;EACrE3B,wBAAwB,CAAC6B,GAAG,CAACN,OAAO,EAAEK,0BAA0B,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,WAAWA,CACzBT,OAAuC,EACvC,GAAGC,IAAU,EACA;EACb,OAAOS,sBAAsB,CAACV,OAAO,CAAC,CAAC,GAAGC,IAAI,CAAC;AACjD;;AAEA;;AAKA,OAAO,SAASS,sBAAsBA,CACpCV,OAA2C,EACX;EAChC,OAAO,CAAC,GAAGC,IAAI,KAAK;IAClB,OAAOnB,cAAc,CAAC4B,sBAAsB,CAC1CjC,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACT,MAAMkC,MAAM,GAAGX,OAAO,CAAC,GAAGC,IAAI,CAAC;MAC/B,OAAOvB,+BAA+B,CAACiC,MAAM,CAAC;IAChD,CAAC,CACH,CAAC;EACH,CAAC;AACH;AAcA,SAASC,cAAcA,CACrBZ,OAA2C,EAC3C,GAAGC,IAAU,EACP;EACN;EACAD,OAAO,CAAC,GAAGC,IAAI,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,OAAOA,CACrBC,GAGsC,EACb;EACzB,SAAS;;EAET,IACEvC,iBAAiB,IACjBwC,UAAU,CAACC,cAAc,KAAKxC,WAAW,CAACyC,WAAW,EACrD;IACA;IACA,OAAO,CAAC,GAAGhB,IAAI,KACbb,cAAc,CACZa,IAAI,CAACP,MAAM,GACP,MAAOoB,GAAG,CAAoC,GAAGb,IAAI,CAAC,GACrDa,GACP,CAAC;EACL;EACA,IAAIlC,iBAAiB,CAAoBkC,GAAG,CAAC,EAAE;IAC7C;IACA;;IAEA,OAAO,CAAC,GAAGb,IAAI,KACbY,OAAO,CAACD,cAAiC,CAAC,CACxCE,GAAG,EACH,GAAGb,IACL,CAAC;EACL;EACA,IAAKa,GAAG,CAAkBI,gBAAgB,EAAE;IAC1C;IACA;IACA;IACA;IACAJ,GAAG,GAAIA,GAAG,CAAkBI,gBAAgB;EAC9C;EAEA,MAAMC,YAAY,GAChB,OAAOL,GAAG,KAAK,UAAU,GACrB3B,MAAM,CAACiC,yBAAyB,GAChCjC,MAAM,CAACkC,2BAA2B;EAExC,OAAO,CAAC,GAAGpB,IAAI,KAAK;IAClBkB,YAAY,CACVL,GAAG,EAGHb,IAAI,CAACP,MAAM,GAAG,CAAC,GAAGhB,+BAA+B,CAACuB,IAAI,CAAC,GAAGqB,SAC5D,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAC1BT,GAGsC,EACtC,GAAGb,IAAU,EACP;EACN,SAAS;;EACTY,OAAO,CAACC,GAAG,CAAC,CAAC,GAAGb,IAAI,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuB,YAAYA,CAC1BxB,OAAuC,EACE;EACzC,IAAIG,OAAO,IAAI,CAAC5B,iBAAiB,IAAI,CAACK,iBAAiB,CAACoB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAInB,aAAa,CAAC,gDAAgD,CAAC;EAC3E;EACA,OAAO,CAAC,GAAGoB,IAAU,KAAK;IACxB,OAAO,IAAIwB,OAAO,CAAeC,OAAO,IAAK;MAC3C,IAAIpD,OAAO,EAAE;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAQ,cAAc,CAACiB,YAAY,CACzBtB,kBAAkB,CAAC,MAAM;UACvB,SAAS;;UACTuB,OAAO,CAAC,GAAGC,IAAI,CAAC;QAClB,CAAC,CACH,CAAC;QACD;MACF;MACA,IAAIE,OAAO,EAAE;QACX;QACA;QACA;QACA;QACA;QACA1B,kBAAkB,CAACuB,OAAO,CAAC;QAC3BvB,kBAAkB,CAACwB,IAAI,CAAC;MAC1B;MAEAI,SAAS,CAACL,OAAO,EAAwCC,IAAI,EAAEyB,OAAO,CAAC;IACzE,CAAC,CAAC;EACJ,CAAC;AACH;AAEA,IAAIvB,OAAO,EAAE;EACX,SAASwB,mBAAmBA,CAAA,EAAS;IACnC,SAAS;;IACT,MAAM,IAAI9C,aAAa,CACrB,uJACF,CAAC;EACH;EAEA,MAAM+C,+BAA+B,GACnCnD,kBAAkB,CAACkD,mBAAmB,CAAC;EACzChD,wBAAwB,CAAC6B,GAAG,CAACgB,YAAY,EAAEI,+BAA+B,CAAC;AAC7E;AAEA,SAASvB,SAASA,CAChBL,OAA2C,EAC3CC,IAAU,EACVyB,OAAsC,EAChC;EACN,MAAMG,GAAG,GAAG,CAAC7B,OAAO,EAAEC,IAAI,EAAEyB,OAAO,CAA6B;EAChE3C,YAAY,CAACO,IAAI,CAACuC,GAAuB,CAAC;EAC1C,IAAI9C,YAAY,CAACW,MAAM,KAAK,CAAC,EAAE;IAC7BoC,YAAY,CAAC,CAAC;EAChB;AACF;AAEA,SAASA,YAAYA,CAAA,EAAS;EAC5B1C,cAAc,CAAC,MAAM;IACnB,MAAM2C,KAAK,GAAGhD,YAAY;IAC1BA,YAAY,GAAG,EAAE;IACjBD,cAAc,CAACiB,YAAY,CACzBtB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTsD,KAAK,CAACpC,OAAO,CAAC,CAAC,CAACqC,eAAe,EAAEC,WAAW,EAAEC,UAAU,CAAC,KAAK;QAC5D,MAAMvB,MAAM,GAAGqB,eAAe,CAAC,GAAGC,WAAW,CAAC;QAC9C,IAAIC,UAAU,EAAE;UACdrB,OAAO,CAACqB,UAAU,CAAC,CAACvB,MAAM,CAAC;QAC7B;MACF,CAAC,CAAC;MACFb,cAAc,CAAC,CAAC;IAClB,CAAC,CACH,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqC,sBAAsBA,CACpCnC,OAAiC,EACjC;EACA,OAAO,CAAC,GAAGC,IAAW,KAAK;IACzB,SAAS;;IACTD,OAAO,CAAC,GAAGC,IAAI,CAAC;IAChBH,cAAc,CAAC,CAAC;EAClB,CAAC;AACH", "ignoreList": []}