{"version": 3, "names": ["pushMicrotask", "setupQueueMicrotask", "globalThis", "queueMicrotask", "callback", "args"], "sourceRoot": "../../../../src", "sources": ["runLoop/workletRuntime/queueMicrotask.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAa,QAAQ,gBAAa;AAE3C,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpC,SAAS;;EACTC,UAAU,CAACC,cAAc,GAAG,UAC1BC,QAAsC,EACtC,GAAGC,IAAe,EAClB;IACAL,aAAa,CAAC,MAAMI,QAAQ,CAAC,GAAGC,IAAI,CAAC,CAAC;EACxC,CAA0B;AAC5B", "ignoreList": []}