{"version": 3, "names": ["setupRequestAnimationFrame", "animationQueuePollingRate", "timeoutInterval", "queuedCallbacks", "queuedCallbacksBegin", "queuedCallbacksEnd", "flushedCallbacks", "flushedCallbacksBegin", "flushedCallbacksEnd", "flushRequested", "flushAnimationFrame", "timestamp", "performance", "now", "callback", "globalThis", "__flushMicrotasks", "requestAnimationFrame", "handle", "push", "setTimeout", "cancelAnimationFrame"], "sourceRoot": "../../../../src", "sources": ["runLoop/workletRuntime/requestAnimationFramePolyfill.ts"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,SAASA,0BAA0BA,CAACC,yBAAiC,EAAE;EAC5E,SAAS;;EACT,MAAMC,eAAe,GAAGD,yBAAyB;EAEjD,IAAIE,eAAgD,GAAG,EAAE;EACzD,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,kBAAkB,GAAG,CAAC;EAE1B,IAAIC,gBAAgB,GAAGH,eAAe;EACtC,IAAII,qBAAqB,GAAG,CAAC;EAC7B,IAAIC,mBAAmB,GAAG,CAAC;EAE3B,IAAIC,cAAc,GAAG,KAAK;EAE1B,SAASC,mBAAmBA,CAAA,EAAG;IAC7BD,cAAc,GAAG,KAAK;IACtB,MAAME,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IAEnCP,gBAAgB,GAAGH,eAAe;IAClCA,eAAe,GAAG,EAAE;IAEpBI,qBAAqB,GAAGH,oBAAoB;IAC5CI,mBAAmB,GAAGH,kBAAkB;IACxCD,oBAAoB,GAAGC,kBAAkB;IAEzC,KAAK,MAAMS,QAAQ,IAAIR,gBAAgB,EAAE;MACvCQ,QAAQ,CAACH,SAAS,CAAC;MACnBI,UAAU,CAACC,iBAAiB,CAAC,CAAC;IAChC;IAEAT,qBAAqB,GAAGC,mBAAmB;EAC7C;EAEAO,UAAU,CAACE,qBAAqB,GAC9BH,QAAqC,IAC1B;IACX,MAAMI,MAAM,GAAGb,kBAAkB,EAAE;IAEnCF,eAAe,CAACgB,IAAI,CAACL,QAAQ,CAAC;IAC9B,IAAI,CAACL,cAAc,EAAE;MACnBA,cAAc,GAAG,IAAI;MAErBW,UAAU,CAACV,mBAAmB,EAAER,eAAe,CAAC;IAClD;IACA,OAAOgB,MAAM;EACf,CAAC;EAEDH,UAAU,CAACM,oBAAoB,GAAIH,MAAc,IAAK;IACpD,IAAIA,MAAM,GAAGX,qBAAqB,IAAIW,MAAM,IAAIb,kBAAkB,EAAE;MAClE;IACF;IAEA,IAAIa,MAAM,GAAGV,mBAAmB,EAAE;MAChCF,gBAAgB,CAACY,MAAM,GAAGX,qBAAqB,CAAC,GAAG,MAAM,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLJ,eAAe,CAACe,MAAM,GAAGd,oBAAoB,CAAC,GAAG,MAAM,CAAC,CAAC;IAC3D;EACF,CAAC;AACH", "ignoreList": []}