{"version": 3, "names": ["callMicrotasks", "setupRequestAnimationFrame", "nativeRequestAnimationFrame", "globalThis", "requestAnimationFrame", "queuedCallbacks", "queuedCallbacksBegin", "queuedCallbacksEnd", "flushedCallbacks", "flushedCallbacksBegin", "flushedCallbacksEnd", "flushRequested", "__flushAnimationFrame", "timestamp", "callback", "handle", "push", "__frameTimestamp", "undefined", "cancelAnimationFrame"], "sourceRoot": "../../../../src", "sources": ["runLoop/uiRuntime/requestAnimationFrame.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,kBAAe;AAE9C,OAAO,SAASC,0BAA0BA,CAAA,EAAG;EAC3C,SAAS;;EACT,MAAMC,2BAA2B,GAAGC,UAAU,CAACC,qBAAqB;EAEpE,IAAIC,eAAgD,GAAG,EAAE;EACzD,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,kBAAkB,GAAG,CAAC;EAE1B,IAAIC,gBAAgB,GAAGH,eAAe;EACtC,IAAII,qBAAqB,GAAG,CAAC;EAC7B,IAAIC,mBAAmB,GAAG,CAAC;EAE3B,IAAIC,cAAc,GAAG,KAAK;EAE1BR,UAAU,CAACS,qBAAqB,GAAIC,SAAiB,IAAK;IACxDL,gBAAgB,GAAGH,eAAe;IAClCA,eAAe,GAAG,EAAE;IAEpBI,qBAAqB,GAAGH,oBAAoB;IAC5CI,mBAAmB,GAAGH,kBAAkB;IACxCD,oBAAoB,GAAGC,kBAAkB;IAEzC,KAAK,MAAMO,QAAQ,IAAIN,gBAAgB,EAAE;MACvCM,QAAQ,CAACD,SAAS,CAAC;IACrB;IAEAJ,qBAAqB,GAAGC,mBAAmB;IAE3CV,cAAc,CAAC,CAAC;EAClB,CAAC;EAEDG,UAAU,CAACC,qBAAqB,GAC9BU,QAAqC,IAC1B;IACX,MAAMC,MAAM,GAAGR,kBAAkB,EAAE;IAEnCF,eAAe,CAACW,IAAI,CAACF,QAAQ,CAAC;IAC9B,IAAI,CAACH,cAAc,EAAE;MACnBA,cAAc,GAAG,IAAI;MAErBT,2BAA2B,CAAEW,SAAS,IAAK;QACzCF,cAAc,GAAG,KAAK;QACtBR,UAAU,CAACc,gBAAgB,GAAGJ,SAAS;QACvCV,UAAU,CAACS,qBAAqB,CAACC,SAAS,CAAC;QAC3CV,UAAU,CAACc,gBAAgB,GAAGC,SAAS;MACzC,CAAC,CAAC;IACJ;IACA,OAAOH,MAAM;EACf,CAAC;EAEDZ,UAAU,CAACgB,oBAAoB,GAAIJ,MAAc,IAAK;IACpD,IAAIA,MAAM,GAAGN,qBAAqB,IAAIM,MAAM,IAAIR,kBAAkB,EAAE;MAClE;IACF;IAEA,IAAIQ,MAAM,GAAGL,mBAAmB,EAAE;MAChCF,gBAAgB,CAACO,MAAM,GAAGN,qBAAqB,CAAC,GAAG,MAAM,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLJ,eAAe,CAACU,MAAM,GAAGT,oBAAoB,CAAC,GAAG,MAAM,CAAC,CAAC;IAC3D;EACF,CAAC;AACH", "ignoreList": []}