{"version": 3, "names": ["setupSetInterval", "intervalHandleToTimeoutHandle", "Map", "setIntervalPolyfill", "callback", "delay", "args", "intervalHandle", "repeating<PERSON>allback", "timeoutH<PERSON>le", "setTimeout", "set", "clearIntervalPolyfill", "get", "clearTimeout", "delete", "globalThis", "setInterval", "clearInterval"], "sourceRoot": "../../../../src", "sources": ["runLoop/common/setIntervalPolyfill.ts"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,SAASA,gBAAgBA,CAAA,EAAG;EACjC,SAAS;;EAET,MAAMC,6BAAkD,GAAG,IAAIC,GAAG,CAAC,CAAC;EAEpE,MAAMC,mBAAmB,GAAGA,CAC1BC,QAAsC,EACtCC,KAAa,GAAG,CAAC,EACjB,GAAGC,IAAe,KACf;IACH,IAAIC,cAAc,GAAG,CAAC;IAEtB,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAMC,aAAa,GAAGC,UAAU,CAC9BF,iBAAiB,EACjBH,KACF,CAAsB;MACtBJ,6BAA6B,CAACU,GAAG,CAACJ,cAAc,EAAEE,aAAa,CAAC;MAChEL,QAAQ,CAAC,GAAGE,IAAI,CAAC;IACnB,CAAC;IAEDC,cAAc,GAAGG,UAAU,CAACF,iBAAiB,EAAEH,KAAK,CAAsB;IAC1EJ,6BAA6B,CAACU,GAAG,CAACJ,cAAc,EAAEA,cAAc,CAAC;IAEjE,OAAOA,cAAc;EACvB,CAAC;EAED,MAAMK,qBAAqB,GAAIL,cAAsB,IAAK;IACxD,MAAME,aAAa,GAAGR,6BAA6B,CAACY,GAAG,CAACN,cAAc,CAAC;IACvEO,YAAY,CAACL,aAAa,CAAC;IAC3BR,6BAA6B,CAACc,MAAM,CAACR,cAAc,CAAC;EACtD,CAAC;EAEDS,UAAU,CAACC,WAAW,GAAGd,mBAAyC;EAClEa,UAAU,CAACE,aAAa,GAAGN,qBAA6C;AAC1E", "ignoreList": []}