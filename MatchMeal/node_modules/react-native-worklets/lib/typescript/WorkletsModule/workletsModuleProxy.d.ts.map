{"version": 3, "file": "workletsModuleProxy.d.ts", "sourceRoot": "", "sources": ["../../../src/WorkletsModule/workletsModuleProxy.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAC3D,OAAO,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAEvE,yDAAyD;AACzD,MAAM,WAAW,mBAAmB;IAClC,kBAAkB,CAAC,MAAM,EACvB,KAAK,EAAE,MAAM,EACb,mBAAmB,EAAE,OAAO,EAC5B,iBAAiB,CAAC,EAAE,MAAM,GACzB,eAAe,CAAC,MAAM,CAAC,CAAC;IAE3B,wBAAwB,CAAC,MAAM,EAC7B,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,GACf,eAAe,CAAC,MAAM,CAAC,CAAC;IAE3B,wBAAwB,CAAC,GAAG,EAAE,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IAE/D,wBAAwB,CAAC,GAAG,EAAE,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IAE/D,yBAAyB,CAAC,IAAI,EAAE,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;IAEnE,wBAAwB,CAAC,MAAM,EAAE,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IAElE,2BAA2B,IAAI,eAAe,CAAC,SAAS,CAAC,CAAC;IAE1D,sBAAsB,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;IAEhD,iCAAiC,CAC/B,MAAM,SAAS,MAAM,EACrB,MAAM,SAAS,MAAM,EAErB,KAAK,EAAE,MAAM,EACb,KAAK,EAAE,MAAM,GACZ,eAAe,CAAC,MAAM,CAAC,CAAC;IAE3B,wBAAwB,CAAC,CAAC,SAAS,MAAM,EACvC,GAAG,EAAE,CAAC,EACN,kBAAkB,EAAE,OAAO,EAC3B,iBAAiB,CAAC,EAAE,MAAM,GACzB,eAAe,CAAC,CAAC,CAAC,CAAC;IAEtB,4BAA4B,CAAC,CAAC,SAAS,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;IAE3E,uBAAuB,CACrB,KAAK,EAAE,OAAO,EAAE,EAChB,kBAAkB,EAAE,OAAO,GAC1B,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;IAE9B,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAChC,IAAI,EAAE,IAAI,EAAE,EACZ,MAAM,EAAE,MAAM,EAAE,GACf,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAEtC,qBAAqB,CAAC,OAAO,EAC3B,MAAM,EAAE,OAAO,EAAE,GAChB,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IAEjC,6BAA6B,CAAC,GAAG,EAAE,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IAEpE,0BAA0B,CAAC,KAAK,SAAS,OAAO,EAAE,EAAE,OAAO,EACzD,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,KAAK,KAAK,OAAO,GAChC,eAAe,CAAC,OAAO,CAAC,CAAC;IAE5B,yBAAyB,CACvB,OAAO,EAAE,MAAM,EACf,mBAAmB,EAAE,OAAO,GAC3B,eAAe,CAAC,MAAM,CAAC,CAAC;IAE3B,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;IAElE,sBAAsB,CAAC,MAAM,EAAE,OAAO,EACpC,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,GACpC,OAAO,CAAC;IAEX,oBAAoB,CAClB,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,eAAe,CAAC,MAAM,IAAI,CAAC,EACxC,eAAe,EAAE,OAAO,EACxB,WAAW,EAAE,MAAM,GAAG,SAAS,EAC/B,eAAe,EAAE,OAAO,GACvB,cAAc,CAAC;IAElB,iBAAiB,CAAC,MAAM,EACtB,cAAc,EAAE,cAAc,EAC9B,OAAO,EAAE,eAAe,CAAC,MAAM,CAAC,GAC/B,IAAI,CAAC;IAER,oBAAoB,CAClB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,MAAM,GACf,IAAI,CAAC;IAER,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAEvE,sBAAsB,CAAC,MAAM,EAC3B,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,CAAC,GAC3C,MAAM,CAAC;IAEV,yBAAyB,CAAC,MAAM,EAC9B,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,CAAC,GAC3C,MAAM,CAAC;IAEV,yBAAyB,CAAC,MAAM,EAC9B,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,CAAC,EAC5C,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,GAC7B,IAAI,CAAC;IAER,kBAAkB,CAAC,MAAM,EACvB,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,CAAC,GAC3C,IAAI,CAAC;IAER,oBAAoB,CAAC,MAAM,EACzB,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,CAAC,GAC3C,IAAI,CAAC;IAER,oBAAoB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC;IAE5C,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC;CAC3D;AAED,MAAM,MAAM,eAAe,GAAG,mBAAmB,CAAC"}