import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import OnboardingScreen from './src/screens/OnboardingScreen';
import { UserPreferences } from './src/types';
import { loadUserPreferences, saveUserPreferences } from './src/utils/storage';

export default function App() {
  const [userPreferences, setUserPreferences] = useState<UserPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const preferences = await loadUserPreferences();
      setUserPreferences(preferences);

      // Check if this is the first time opening the app
      const hasSeenOnboarding = await loadUserPreferences().then(prefs =>
        prefs.likedMeals.length > 0 || prefs.dislikedMeals.length > 0
      );
      setShowOnboarding(!hasSeenOnboarding);
    } catch (error) {
      console.error('Error loading preferences:', error);
      // Fallback to default preferences and show onboarding
      setUserPreferences({
        likedMeals: [],
        dislikedMeals: [],
        dietaryRestrictions: [],
        allergies: [],
        preferredCuisines: [],
      });
      setShowOnboarding(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdatePreferences = async (preferences: UserPreferences) => {
    setUserPreferences(preferences);
    await saveUserPreferences(preferences);
  };

  if (isLoading || !userPreferences) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>MatchMeal</Text>
        <Text style={styles.loadingSubtext}>Chargement...</Text>
      </View>
    );
  }

  if (showOnboarding) {
    return (
      <OnboardingScreen onComplete={() => setShowOnboarding(false)} />
    );
  }

  return (
    <View style={styles.container}>
      <AppNavigator
        userPreferences={userPreferences}
        onUpdatePreferences={handleUpdatePreferences}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF6B35',
    marginBottom: 10,
  },
  loadingSubtext: {
    fontSize: 16,
    color: '#666',
  },
});
