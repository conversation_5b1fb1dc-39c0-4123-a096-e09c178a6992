import React from 'react';
import {
  View,
  StyleSheet,
} from 'react-native';
import { Meal } from '../types';
import MealCard from './MealCard';

interface SwipeableCardProps {
  meal: Meal;
  onSwipeLeft: (meal: Meal) => void;
  onSwipeRight: (meal: Meal) => void;
  onPress?: () => void;
  isTop?: boolean;
}

const SwipeableCard: React.FC<SwipeableCardProps> = ({
  meal,
  onSwipeLeft,
  onSwipeRight,
  onPress,
  isTop = false,
}) => {
  return (
    <View style={[styles.container, { opacity: isTop ? 1 : 0.8, transform: [{ scale: isTop ? 1 : 0.95 }] }]}>
      <MealCard meal={meal} onPress={onPress} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
  },
});

export default SwipeableCard;
