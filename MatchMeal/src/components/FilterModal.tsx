import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, borderRadius } from '../utils/theme';

export interface FilterOptions {
  cuisines: string[];
  difficulties: string[];
  maxPrepTime: number;
  maxCookTime: number;
  dietaryRestrictions: string[];
  maxCalories: number;
  minRating: number;
}

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: FilterOptions) => void;
  currentFilters: FilterOptions;
}

const CUISINES = [
  'Française', 'Italienne', 'Asiatique', 'Mexicaine', 'Indienne',
  'Thaïlandaise', 'Japonaise', 'Méditerranéenne', 'Américaine', 'Libanaise'
];

const DIFFICULTIES = ['Facile', 'Moyen', 'Difficile'];

const DIETARY_RESTRICTIONS = [
  'Végétarien', 'Végétalien', 'Sans gluten', 'Sans lactose',
  'Sans noix', 'Halal', 'Casher', 'Paléo', 'Keto'
];

const PREP_TIME_OPTIONS = [15, 30, 45, 60, 90, 120];
const CALORIE_OPTIONS = [300, 500, 700, 1000, 1500];
const RATING_OPTIONS = [3, 3.5, 4, 4.5, 5];

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  onClose,
  onApplyFilters,
  currentFilters,
}) => {
  const [filters, setFilters] = useState<FilterOptions>(currentFilters);

  const toggleCuisine = (cuisine: string) => {
    setFilters(prev => ({
      ...prev,
      cuisines: prev.cuisines.includes(cuisine)
        ? prev.cuisines.filter(c => c !== cuisine)
        : [...prev.cuisines, cuisine]
    }));
  };

  const toggleDifficulty = (difficulty: string) => {
    setFilters(prev => ({
      ...prev,
      difficulties: prev.difficulties.includes(difficulty)
        ? prev.difficulties.filter(d => d !== difficulty)
        : [...prev.difficulties, difficulty]
    }));
  };

  const toggleDietaryRestriction = (restriction: string) => {
    setFilters(prev => ({
      ...prev,
      dietaryRestrictions: prev.dietaryRestrictions.includes(restriction)
        ? prev.dietaryRestrictions.filter(r => r !== restriction)
        : [...prev.dietaryRestrictions, restriction]
    }));
  };

  const resetFilters = () => {
    setFilters({
      cuisines: [],
      difficulties: [],
      maxPrepTime: 120,
      maxCookTime: 120,
      dietaryRestrictions: [],
      maxCalories: 1500,
      minRating: 0,
    });
  };

  const applyFilters = () => {
    onApplyFilters(filters);
    onClose();
  };

  const renderFilterSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderToggleButtons = (
    items: string[],
    selectedItems: string[],
    onToggle: (item: string) => void
  ) => (
    <View style={styles.toggleContainer}>
      {items.map(item => (
        <TouchableOpacity
          key={item}
          style={[
            styles.toggleButton,
            selectedItems.includes(item) && styles.toggleButtonActive
          ]}
          onPress={() => onToggle(item)}
        >
          <Text style={[
            styles.toggleButtonText,
            selectedItems.includes(item) && styles.toggleButtonTextActive
          ]}>
            {item}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSliderOptions = (
    options: number[],
    currentValue: number,
    onSelect: (value: number) => void,
    suffix: string = ''
  ) => (
    <View style={styles.sliderContainer}>
      {options.map(option => (
        <TouchableOpacity
          key={option}
          style={[
            styles.sliderOption,
            currentValue === option && styles.sliderOptionActive
          ]}
          onPress={() => onSelect(option)}
        >
          <Text style={[
            styles.sliderOptionText,
            currentValue === option && styles.sliderOptionTextActive
          ]}>
            {option}{suffix}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Filtres</Text>
          <TouchableOpacity onPress={resetFilters} style={styles.resetButton}>
            <Text style={styles.resetText}>Reset</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderFilterSection(
            'Types de cuisine',
            renderToggleButtons(CUISINES, filters.cuisines, toggleCuisine)
          )}

          {renderFilterSection(
            'Niveau de difficulté',
            renderToggleButtons(DIFFICULTIES, filters.difficulties, toggleDifficulty)
          )}

          {renderFilterSection(
            'Temps de préparation max',
            renderSliderOptions(
              PREP_TIME_OPTIONS,
              filters.maxPrepTime,
              (value) => setFilters(prev => ({ ...prev, maxPrepTime: value })),
              ' min'
            )
          )}

          {renderFilterSection(
            'Calories max',
            renderSliderOptions(
              CALORIE_OPTIONS,
              filters.maxCalories,
              (value) => setFilters(prev => ({ ...prev, maxCalories: value })),
              ' cal'
            )
          )}

          {renderFilterSection(
            'Note minimum',
            renderSliderOptions(
              RATING_OPTIONS,
              filters.minRating,
              (value) => setFilters(prev => ({ ...prev, minRating: value })),
              ' ⭐'
            )
          )}

          {renderFilterSection(
            'Restrictions alimentaires',
            renderToggleButtons(
              DIETARY_RESTRICTIONS,
              filters.dietaryRestrictions,
              toggleDietaryRestriction
            )
          )}
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
            <Text style={styles.applyButtonText}>Appliquer les filtres</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  closeButton: {
    padding: spacing.sm,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  resetButton: {
    padding: spacing.sm,
  },
  resetText: {
    color: colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  section: {
    marginVertical: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  toggleContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  toggleButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  toggleButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  toggleButtonText: {
    color: colors.text.secondary,
    fontSize: 14,
  },
  toggleButtonTextActive: {
    color: colors.text.white,
    fontWeight: '600',
  },
  sliderContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  sliderOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    minWidth: 60,
    alignItems: 'center',
  },
  sliderOptionActive: {
    backgroundColor: colors.secondary,
    borderColor: colors.secondary,
  },
  sliderOptionText: {
    color: colors.text.secondary,
    fontSize: 14,
  },
  sliderOptionTextActive: {
    color: colors.text.white,
    fontWeight: '600',
  },
  footer: {
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  applyButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    alignItems: 'center',
  },
  applyButtonText: {
    color: colors.text.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FilterModal;
