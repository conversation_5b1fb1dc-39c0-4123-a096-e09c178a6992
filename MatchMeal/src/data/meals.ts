import { Meal } from '../types';

export const mealsData: Meal[] = [
  {
    id: '1',
    name: 'Spaghetti Carbonara',
    description: 'Un classique italien crémeux avec des œufs, du parmesan et des lardons',
    image: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=300&fit=crop',
    prepTime: 10,
    cookTime: 15,
    difficulty: 'Moyen',
    servings: 4,
    category: 'Plat principal',
    cuisine: 'Italienne',
    tags: ['pâtes', 'crémeux', 'rapide'],
    rating: 4.8,
    calories: 520,
    recipe: {
      ingredients: [
        { id: '1', name: 'Spaghetti', quantity: 400, unit: 'g' },
        { id: '2', name: 'Lardon<PERSON>', quantity: 200, unit: 'g' },
        { id: '3', name: 'Œufs', quantity: 3, unit: 'pièces' },
        { id: '4', name: 'Parmesan râpé', quantity: 100, unit: 'g' },
        { id: '5', name: 'Poivre noir', quantity: 1, unit: 'cuillère à café' }
      ],
      instructions: [
        { id: '1', step: 1, description: 'Faire cuire les spaghetti dans de l\'eau bouillante salée', duration: 10 },
        { id: '2', step: 2, description: 'Faire revenir les lardons dans une poêle', duration: 5 },
        { id: '3', step: 3, description: 'Battre les œufs avec le parmesan et le poivre', duration: 2 },
        { id: '4', step: 4, description: 'Mélanger les pâtes chaudes avec le mélange œuf-fromage', duration: 3 }
      ],
      tips: ['Ne pas faire cuire les œufs, la chaleur des pâtes suffit', 'Servir immédiatement']
    }
  },
  {
    id: '2',
    name: 'Salade César',
    description: 'Salade croquante avec poulet grillé, croûtons et sauce césar maison',
    image: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=300&fit=crop',
    prepTime: 20,
    cookTime: 10,
    difficulty: 'Facile',
    servings: 2,
    category: 'Entrée',
    cuisine: 'Américaine',
    tags: ['salade', 'poulet', 'léger'],
    rating: 4.5,
    calories: 350,
    recipe: {
      ingredients: [
        { id: '2-1', name: 'Laitue romaine', quantity: 2, unit: 'têtes' },
        { id: '2-2', name: 'Blanc de poulet', quantity: 300, unit: 'g' },
        { id: '2-3', name: 'Pain de mie', quantity: 4, unit: 'tranches' },
        { id: '2-4', name: 'Parmesan', quantity: 50, unit: 'g' },
        { id: '2-5', name: 'Mayonnaise', quantity: 3, unit: 'cuillères à soupe' }
      ],
      instructions: [
        { id: '2-1', step: 1, description: 'Griller le poulet et le découper en lamelles', duration: 10 },
        { id: '2-2', step: 2, description: 'Préparer les croûtons au four', duration: 8 },
        { id: '2-3', step: 3, description: 'Laver et couper la salade', duration: 5 },
        { id: '2-4', step: 4, description: 'Préparer la sauce césar et assembler', duration: 5 }
      ]
    }
  },
  {
    id: '3',
    name: 'Ratatouille Provençale',
    description: 'Légumes du soleil mijotés aux herbes de Provence',
    image: 'https://images.unsplash.com/photo-1572441713132-51c75654db73?w=400&h=300&fit=crop',
    prepTime: 25,
    cookTime: 45,
    difficulty: 'Facile',
    servings: 6,
    category: 'Plat principal',
    cuisine: 'Française',
    tags: ['végétarien', 'légumes', 'provençal'],
    rating: 4.3,
    calories: 180,
    recipe: {
      ingredients: [
        { id: '3-1', name: 'Aubergines', quantity: 2, unit: 'pièces' },
        { id: '3-2', name: 'Courgettes', quantity: 3, unit: 'pièces' },
        { id: '3-3', name: 'Tomates', quantity: 4, unit: 'pièces' },
        { id: '3-4', name: 'Poivrons', quantity: 2, unit: 'pièces' },
        { id: '3-5', name: 'Herbes de Provence', quantity: 2, unit: 'cuillères à café' }
      ],
      instructions: [
        { id: '3-1', step: 1, description: 'Découper tous les légumes en dés', duration: 15 },
        { id: '3-2', step: 2, description: 'Faire revenir les légumes séparément', duration: 20 },
        { id: '3-3', step: 3, description: 'Assembler et laisser mijoter', duration: 25 }
      ]
    }
  },
  {
    id: '4',
    name: 'Pad Thaï aux Crevettes',
    description: 'Nouilles de riz sautées aux crevettes, sauce tamarind et cacahuètes',
    image: 'https://images.unsplash.com/photo-1559314809-0f31657def5e?w=400&h=300&fit=crop',
    prepTime: 15,
    cookTime: 12,
    difficulty: 'Moyen',
    servings: 3,
    category: 'Plat principal',
    cuisine: 'Thaïlandaise',
    tags: ['nouilles', 'crevettes', 'épicé'],
    rating: 4.7,
    calories: 420,
    recipe: {
      ingredients: [
        { id: '4-1', name: 'Nouilles de riz', quantity: 300, unit: 'g' },
        { id: '4-2', name: 'Crevettes', quantity: 250, unit: 'g' },
        { id: '4-3', name: 'Sauce de poisson', quantity: 2, unit: 'cuillères à soupe' },
        { id: '4-4', name: 'Cacahuètes', quantity: 50, unit: 'g' },
        { id: '4-5', name: 'Pousses de soja', quantity: 100, unit: 'g' }
      ],
      instructions: [
        { id: '4-1', step: 1, description: 'Faire tremper les nouilles dans l\'eau chaude', duration: 8 },
        { id: '4-2', step: 2, description: 'Faire sauter les crevettes', duration: 4 },
        { id: '4-3', step: 3, description: 'Ajouter les nouilles et la sauce', duration: 5 },
        { id: '4-4', step: 4, description: 'Garnir de cacahuètes et pousses de soja', duration: 2 }
      ]
    }
  },
  {
    id: '5',
    name: 'Tiramisu Classique',
    description: 'Dessert italien au mascarpone, café et cacao',
    image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400&h=300&fit=crop',
    prepTime: 30,
    cookTime: 0,
    difficulty: 'Moyen',
    servings: 8,
    category: 'Dessert',
    cuisine: 'Italienne',
    tags: ['dessert', 'café', 'mascarpone'],
    rating: 4.9,
    calories: 380,
    recipe: {
      ingredients: [
        { id: '5-1', name: 'Mascarpone', quantity: 500, unit: 'g' },
        { id: '5-2', name: 'Œufs', quantity: 6, unit: 'pièces' },
        { id: '5-3', name: 'Sucre', quantity: 100, unit: 'g' },
        { id: '5-4', name: 'Biscuits à la cuillère', quantity: 300, unit: 'g' },
        { id: '5-5', name: 'Café fort', quantity: 300, unit: 'ml' }
      ],
      instructions: [
        { id: '5-1', step: 1, description: 'Séparer les blancs des jaunes d\'œufs', duration: 5 },
        { id: '5-2', step: 2, description: 'Mélanger jaunes, sucre et mascarpone', duration: 10 },
        { id: '5-3', step: 3, description: 'Monter les blancs en neige et incorporer', duration: 8 },
        { id: '5-4', step: 4, description: 'Tremper les biscuits et monter le tiramisu', duration: 15 }
      ],
      tips: ['Laisser reposer au frais 4h minimum', 'Saupoudrer de cacao avant de servir']
    }
  },
  {
    id: '6',
    name: 'Burger Végétarien',
    description: 'Steak de légumes maison avec avocat et sauce tahini',
    image: 'https://images.unsplash.com/photo-1520072959219-c595dc870360?w=400&h=300&fit=crop',
    prepTime: 20,
    cookTime: 15,
    difficulty: 'Moyen',
    servings: 4,
    category: 'Plat principal',
    cuisine: 'Moderne',
    tags: ['végétarien', 'burger', 'avocat'],
    rating: 4.4,
    calories: 450,
    recipe: {
      ingredients: [
        { id: '6-1', name: 'Pains burger', quantity: 4, unit: 'pièces' },
        { id: '6-2', name: 'Haricots noirs', quantity: 400, unit: 'g' },
        { id: '6-3', name: 'Avocat', quantity: 2, unit: 'pièces' },
        { id: '6-4', name: 'Tahini', quantity: 3, unit: 'cuillères à soupe' },
        { id: '6-5', name: 'Salade', quantity: 100, unit: 'g' }
      ],
      instructions: [
        { id: '6-1', step: 1, description: 'Écraser les haricots et former des steaks', duration: 10 },
        { id: '6-2', step: 2, description: 'Cuire les steaks à la poêle', duration: 8 },
        { id: '6-3', step: 3, description: 'Préparer la sauce tahini', duration: 5 },
        { id: '6-4', step: 4, description: 'Assembler les burgers', duration: 5 }
      ]
    }
  }
];

export const getRandomMeals = (count: number = 10): Meal[] => {
  const shuffled = [...mealsData].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const getMealById = (id: string): Meal | undefined => {
  return mealsData.find(meal => meal.id === id);
};
