import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Meal, UserPreferences } from '../types';
import { mealsData } from '../data/meals';
import { colors, spacing, borderRadius } from '../utils/theme';

interface SearchScreenProps {
  navigation: any;
  userPreferences: UserPreferences;
  onUpdatePreferences: (preferences: UserPreferences) => void;
}

const SearchScreen: React.FC<SearchScreenProps> = ({
  navigation,
  userPreferences,
  onUpdatePreferences,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredMeals, setFilteredMeals] = useState<Meal[]>([]);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  const cuisineTypes = ['Italienne', 'Française', 'Asiatique', 'Méditerranéenne', 'Mexicaine'];
  const difficultyLevels = ['Facile', 'Moyen', 'Difficile'];
  const mealTypes = ['Petit-déjeuner', 'Déjeuner', 'Dîner', 'Dessert'];

  useEffect(() => {
    filterMeals();
  }, [searchQuery, selectedFilters]);

  const filterMeals = () => {
    let filtered = mealsData;

    // Filtrer par recherche textuelle
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(query) ||
        meal.cuisine.toLowerCase().includes(query) ||
        meal.tags.some(tag => tag.toLowerCase().includes(query)) ||
        meal.recipe.ingredients.some(ingredient => 
          ingredient.name.toLowerCase().includes(query)
        )
      );
    }

    // Filtrer par filtres sélectionnés
    if (selectedFilters.length > 0) {
      filtered = filtered.filter(meal => {
        return selectedFilters.every(filter => {
          return (
            meal.cuisine === filter ||
            meal.difficulty === filter ||
            meal.tags.includes(filter.toLowerCase()) ||
            (filter === 'Petit-déjeuner' && meal.tags.includes('breakfast')) ||
            (filter === 'Déjeuner' && meal.tags.includes('lunch')) ||
            (filter === 'Dîner' && meal.tags.includes('dinner')) ||
            (filter === 'Dessert' && meal.tags.includes('dessert'))
          );
        });
      });
    }

    setFilteredMeals(filtered);
  };

  const toggleFilter = (filter: string) => {
    setSelectedFilters(prev => {
      if (prev.includes(filter)) {
        return prev.filter(f => f !== filter);
      } else {
        return [...prev, filter];
      }
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchQuery('');
  };

  const likeMeal = (mealId: string) => {
    const newPreferences = {
      ...userPreferences,
      likedMeals: [...userPreferences.likedMeals, mealId],
      dislikedMeals: userPreferences.dislikedMeals.filter(id => id !== mealId),
    };
    onUpdatePreferences(newPreferences);
  };

  const renderFilterChips = () => {
    const allFilters = [...cuisineTypes, ...difficultyLevels, ...mealTypes];
    
    return (
      <View style={styles.filtersContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={allFilters}
          keyExtractor={(item) => item}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterChip,
                selectedFilters.includes(item) && styles.filterChipActive
              ]}
              onPress={() => toggleFilter(item)}
            >
              <Text style={[
                styles.filterChipText,
                selectedFilters.includes(item) && styles.filterChipTextActive
              ]}>
                {item}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>
    );
  };

  const renderMealItem = ({ item }: { item: Meal }) => {
    const isLiked = userPreferences.likedMeals.includes(item.id);
    
    return (
      <TouchableOpacity
        style={styles.mealItem}
        onPress={() => navigation.navigate('RecipeDetail', { meal: item })}
      >
        <Image source={{ uri: item.image }} style={styles.mealImage} />
        <View style={styles.mealContent}>
          <View style={styles.mealHeader}>
            <Text style={styles.mealName} numberOfLines={2}>{item.name}</Text>
            <TouchableOpacity
              style={styles.likeButton}
              onPress={() => likeMeal(item.id)}
            >
              <Ionicons
                name={isLiked ? 'heart' : 'heart-outline'}
                size={20}
                color={isLiked ? colors.danger : colors.text.light}
              />
            </TouchableOpacity>
          </View>
          
          <View style={styles.mealMeta}>
            <View style={styles.metaItem}>
              <Ionicons name="flag-outline" size={14} color={colors.text.secondary} />
              <Text style={styles.metaText}>{item.cuisine}</Text>
            </View>
            <View style={styles.metaItem}>
              <Ionicons name="time-outline" size={14} color={colors.text.secondary} />
              <Text style={styles.metaText}>{item.prepTime + item.cookTime} min</Text>
            </View>
            <View style={styles.metaItem}>
              <Ionicons name="star" size={14} color={colors.warning} />
              <Text style={styles.metaText}>{item.rating}</Text>
            </View>
          </View>

          <View style={styles.difficultyBadge}>
            <Text style={styles.difficultyText}>{item.difficulty}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="search-outline" size={80} color={colors.text.light} />
      <Text style={styles.emptyTitle}>Aucun résultat trouvé</Text>
      <Text style={styles.emptySubtitle}>
        Essayez de modifier votre recherche ou vos filtres
      </Text>
      {(searchQuery || selectedFilters.length > 0) && (
        <TouchableOpacity style={styles.clearButton} onPress={clearAllFilters}>
          <Text style={styles.clearButtonText}>Effacer les filtres</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={colors.text.secondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher des recettes..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.text.light}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.text.light} />
            </TouchableOpacity>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.filterToggle}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Ionicons name="options" size={20} color={colors.primary} />
          {selectedFilters.length > 0 && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>{selectedFilters.length}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {showFilters && renderFilterChips()}

      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>
          {filteredMeals.length} recette{filteredMeals.length !== 1 ? 's' : ''} trouvée{filteredMeals.length !== 1 ? 's' : ''}
        </Text>
        {selectedFilters.length > 0 && (
          <TouchableOpacity onPress={clearAllFilters}>
            <Text style={styles.clearFiltersText}>Effacer les filtres</Text>
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={filteredMeals}
        renderItem={renderMealItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.md,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text.primary,
    marginLeft: spacing.sm,
  },
  filterToggle: {
    position: 'relative',
    padding: spacing.sm,
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
  },
  filterBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: colors.danger,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    color: colors.text.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  filtersContainer: {
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  filterChip: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.full,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginHorizontal: spacing.xs,
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterChipActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterChipText: {
    fontSize: 14,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  filterChipTextActive: {
    color: colors.text.white,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
  },
  clearFiltersText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: spacing.lg,
  },
  mealItem: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    marginVertical: spacing.sm,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mealImage: {
    width: 100,
    height: 100,
    resizeMode: 'cover',
  },
  mealContent: {
    flex: 1,
    padding: spacing.md,
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
    marginRight: spacing.sm,
  },
  likeButton: {
    padding: spacing.xs,
  },
  mealMeta: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  metaText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },
  difficultyBadge: {
    alignSelf: 'flex-start',
    backgroundColor: colors.primaryLight,
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
  },
  difficultyText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  clearButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
  },
  clearButtonText: {
    color: colors.text.white,
    fontWeight: '600',
  },
});

export default SearchScreen;
