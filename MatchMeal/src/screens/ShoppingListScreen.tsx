import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, borderRadius } from '../utils/theme';

interface ShoppingItem {
  name: string;
  quantity: number;
  unit: string;
  checked?: boolean;
  category?: string;
}

interface ShoppingListScreenProps {
  route: {
    params: {
      shoppingList: ShoppingItem[];
    };
  };
  navigation: any;
}

const CATEGORIES = {
  'Légumes': ['tomate', 'courgette', 'aubergine', 'poivron', 'oignon', 'ail', 'carotte', 'pomme de terre'],
  'Fruits': ['pomme', 'banane', 'citron', 'orange', 'fraise', 'avocat'],
  'Viandes & Poissons': ['poulet', 'bœuf', 'porc', 'saumon', 'thon', 'crevettes', 'lardons'],
  'Produits laitiers': ['lait', 'beurre', 'fromage', 'yaourt', 'crème', 'mascarpone', 'parmesan'],
  'Épicerie': ['riz', 'pâtes', 'farine', 'sucre', 'sel', 'poivre', 'huile', 'vinaigre'],
  'Herbes & Épices': ['basilic', 'persil', 'thym', 'romarin', 'herbes de provence', 'paprika'],
  'Autres': [],
};

const ShoppingListScreen: React.FC<ShoppingListScreenProps> = ({
  route,
  navigation,
}) => {
  const { shoppingList } = route.params;
  
  const categorizeItems = (items: ShoppingItem[]): { [category: string]: ShoppingItem[] } => {
    const categorized: { [category: string]: ShoppingItem[] } = {};
    
    items.forEach(item => {
      let category = 'Autres';
      
      for (const [cat, keywords] of Object.entries(CATEGORIES)) {
        if (keywords.some(keyword => item.name.toLowerCase().includes(keyword))) {
          category = cat;
          break;
        }
      }
      
      if (!categorized[category]) {
        categorized[category] = [];
      }
      
      categorized[category].push({ ...item, category });
    });
    
    return categorized;
  };

  const [categorizedItems, setCategorizedItems] = useState(categorizeItems(shoppingList));
  const [expandedCategories, setExpandedCategories] = useState<{ [key: string]: boolean }>({});

  const toggleItemCheck = (category: string, itemIndex: number) => {
    setCategorizedItems(prev => {
      const newItems = { ...prev };
      newItems[category][itemIndex].checked = !newItems[category][itemIndex].checked;
      return newItems;
    });
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const getTotalItems = () => {
    return Object.values(categorizedItems).flat().length;
  };

  const getCheckedItems = () => {
    return Object.values(categorizedItems).flat().filter(item => item.checked).length;
  };

  const shareShoppingList = async () => {
    const listText = Object.entries(categorizedItems)
      .map(([category, items]) => {
        const itemsText = items
          .map(item => `• ${item.quantity} ${item.unit} ${item.name}`)
          .join('\n');
        return `${category}:\n${itemsText}`;
      })
      .join('\n\n');

    try {
      await Share.share({
        message: `Ma liste de courses MatchMeal:\n\n${listText}`,
        title: 'Liste de courses MatchMeal',
      });
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de partager la liste de courses');
    }
  };

  const clearCheckedItems = () => {
    Alert.alert(
      'Supprimer les éléments cochés',
      'Êtes-vous sûr de vouloir supprimer tous les éléments cochés ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: () => {
            setCategorizedItems(prev => {
              const newItems: { [category: string]: ShoppingItem[] } = {};
              Object.entries(prev).forEach(([category, items]) => {
                const uncheckedItems = items.filter(item => !item.checked);
                if (uncheckedItems.length > 0) {
                  newItems[category] = uncheckedItems;
                }
              });
              return newItems;
            });
          },
        },
      ]
    );
  };

  const renderCategoryHeader = (category: string, items: ShoppingItem[]) => {
    const isExpanded = expandedCategories[category] !== false; // Par défaut ouvert
    const checkedCount = items.filter(item => item.checked).length;

    return (
      <TouchableOpacity
        style={styles.categoryHeader}
        onPress={() => toggleCategory(category)}
      >
        <View style={styles.categoryHeaderLeft}>
          <Ionicons
            name={isExpanded ? 'chevron-down' : 'chevron-forward'}
            size={20}
            color={colors.text.secondary}
          />
          <Text style={styles.categoryTitle}>{category}</Text>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryBadgeText}>
              {checkedCount}/{items.length}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderShoppingItem = (item: ShoppingItem, category: string, index: number) => (
    <TouchableOpacity
      style={[styles.shoppingItem, item.checked && styles.checkedItem]}
      onPress={() => toggleItemCheck(category, index)}
    >
      <View style={styles.itemLeft}>
        <Ionicons
          name={item.checked ? 'checkmark-circle' : 'ellipse-outline'}
          size={24}
          color={item.checked ? colors.success : colors.text.light}
        />
        <Text style={[styles.itemText, item.checked && styles.checkedText]}>
          {item.quantity} {item.unit} {item.name}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderCategory = ({ item }: { item: [string, ShoppingItem[]] }) => {
    const [category, items] = item;
    const isExpanded = expandedCategories[category] !== false;

    return (
      <View style={styles.categoryContainer}>
        {renderCategoryHeader(category, items)}
        {isExpanded && (
          <View style={styles.categoryItems}>
            {items.map((shoppingItem, index) =>
              renderShoppingItem(shoppingItem, category, index)
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={styles.title}>Liste de courses</Text>
          <Text style={styles.subtitle}>
            {getCheckedItems()}/{getTotalItems()} articles
          </Text>
        </View>
        
        <TouchableOpacity style={styles.shareButton} onPress={shareShoppingList}>
          <Ionicons name="share-outline" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${(getCheckedItems() / getTotalItems()) * 100}%` },
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          {Math.round((getCheckedItems() / getTotalItems()) * 100)}% terminé
        </Text>
      </View>

      <FlatList
        data={Object.entries(categorizedItems)}
        renderItem={renderCategory}
        keyExtractor={([category]) => category}
        style={styles.list}
        showsVerticalScrollIndicator={false}
      />

      {getCheckedItems() > 0 && (
        <View style={styles.footer}>
          <TouchableOpacity style={styles.clearButton} onPress={clearCheckedItems}>
            <Ionicons name="trash-outline" size={20} color={colors.text.white} />
            <Text style={styles.clearButtonText}>
              Supprimer les éléments cochés ({getCheckedItems()})
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  subtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginTop: 2,
  },
  shareButton: {
    padding: spacing.sm,
  },
  progressContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  progressBar: {
    height: 6,
    backgroundColor: colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.success,
  },
  progressText: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  list: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  categoryContainer: {
    marginVertical: spacing.sm,
  },
  categoryHeader: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.xs,
  },
  categoryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginLeft: spacing.sm,
    flex: 1,
  },
  categoryBadge: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
  },
  categoryBadgeText: {
    fontSize: 12,
    color: colors.text.white,
    fontWeight: '600',
  },
  categoryItems: {
    paddingLeft: spacing.lg,
  },
  shoppingItem: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.xs,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  checkedItem: {
    opacity: 0.6,
    backgroundColor: colors.background,
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemText: {
    fontSize: 16,
    color: colors.text.primary,
    marginLeft: spacing.md,
    flex: 1,
  },
  checkedText: {
    textDecorationLine: 'line-through',
    color: colors.text.secondary,
  },
  footer: {
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.danger,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
  },
  clearButtonText: {
    color: colors.text.white,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
});

export default ShoppingListScreen;
