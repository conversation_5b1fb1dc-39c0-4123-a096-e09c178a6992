import React from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  StatusBar,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Meal, UserPreferences } from '../types';
import { getMealById } from '../data/meals';

interface FavoritesScreenProps {
  navigation: any;
  userPreferences: UserPreferences;
  onUpdatePreferences: (preferences: UserPreferences) => void;
}

const FavoritesScreen: React.FC<FavoritesScreenProps> = ({
  navigation,
  userPreferences,
  onUpdatePreferences,
}) => {
  const likedMeals = userPreferences.likedMeals
    .map(id => getMealById(id))
    .filter(meal => meal !== undefined) as Meal[];

  const handleRemoveFromFavorites = (mealId: string) => {
    Alert.alert(
      'Retirer des favoris',
      'Êtes-vous sûr de vouloir retirer ce repas de vos favoris ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Retirer',
          style: 'destructive',
          onPress: () => {
            const updatedPreferences = {
              ...userPreferences,
              likedMeals: userPreferences.likedMeals.filter(id => id !== mealId),
            };
            onUpdatePreferences(updatedPreferences);
          },
        },
      ]
    );
  };

  const renderMealItem = ({ item }: { item: Meal }) => (
    <TouchableOpacity
      style={styles.mealItem}
      onPress={() => navigation.navigate('RecipeDetail', { meal: item })}
    >
      <Image source={{ uri: item.image }} style={styles.mealImage} />
      
      <View style={styles.mealInfo}>
        <Text style={styles.mealName} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={styles.mealCuisine}>{item.cuisine}</Text>
        <Text style={styles.mealDescription} numberOfLines={2}>
          {item.description}
        </Text>
        
        <View style={styles.mealMeta}>
          <View style={styles.metaItem}>
            <Ionicons name="time-outline" size={14} color="#666" />
            <Text style={styles.metaText}>
              {item.prepTime + item.cookTime} min
            </Text>
          </View>
          
          <View style={styles.metaItem}>
            <Ionicons name="restaurant-outline" size={14} color="#666" />
            <Text style={styles.metaText}>{item.servings} pers.</Text>
          </View>
          
          <View style={styles.metaItem}>
            <Ionicons name="star" size={14} color="#FFD700" />
            <Text style={styles.metaText}>{item.rating}</Text>
          </View>
        </View>
      </View>
      
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => handleRemoveFromFavorites(item.id)}
      >
        <Ionicons name="heart" size={24} color="#FF6B35" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={80} color="#ccc" />
      <Text style={styles.emptyTitle}>Aucun favori</Text>
      <Text style={styles.emptySubtitle}>
        Commencez à explorer des repas pour créer votre collection de favoris !
      </Text>
      <TouchableOpacity
        style={styles.exploreButton}
        onPress={() => navigation.navigate('Discover')}
      >
        <Text style={styles.exploreButtonText}>Explorer des repas</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Mes Favoris</Text>
        <Text style={styles.subtitle}>
          {likedMeals.length} repas sauvegardés
        </Text>
      </View>

      {likedMeals.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={likedMeals}
          renderItem={renderMealItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  listContainer: {
    padding: 20,
  },
  mealItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mealImage: {
    width: 80,
    height: 80,
    borderRadius: 10,
    marginRight: 15,
  },
  mealInfo: {
    flex: 1,
  },
  mealName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  mealCuisine: {
    fontSize: 14,
    color: '#FF6B35',
    fontWeight: '500',
    marginBottom: 6,
  },
  mealDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 10,
  },
  mealMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  metaText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#666',
  },
  removeButton: {
    padding: 10,
    alignSelf: 'flex-start',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  exploreButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  exploreButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FavoritesScreen;
