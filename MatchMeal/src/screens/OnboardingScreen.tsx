import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, borderRadius } from '../utils/theme';

const { width: screenWidth } = Dimensions.get('window');

interface OnboardingScreenProps {
  onComplete: () => void;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: '1',
    title: 'Découvrez de nouveaux repas',
    description: 'Swipez à travers une sélection de recettes délicieuses et trouvez vos prochains plats favoris.',
    icon: 'restaurant',
    color: colors.primary,
  },
  {
    id: '2',
    title: 'Sauvegardez vos favoris',
    description: 'Likez les recettes qui vous plaisent pour les retrouver facilement dans votre collection.',
    icon: 'heart',
    color: colors.danger,
  },
  {
    id: '3',
    title: 'Planifiez vos repas',
    description: 'Organisez votre semaine avec notre planificateur de repas intégré.',
    icon: 'calendar',
    color: colors.info,
  },
  {
    id: '4',
    title: 'Mode cuisson guidé',
    description: 'Suivez les recettes étape par étape avec notre mode cuisson interactif.',
    icon: 'play-circle',
    color: colors.success,
  },
  {
    id: '5',
    title: 'Liste de courses automatique',
    description: 'Générez automatiquement votre liste de courses basée sur vos recettes planifiées.',
    icon: 'list',
    color: colors.warning,
  },
];

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);

  const nextStep = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipOnboarding = () => {
    onComplete();
  };

  const renderStep = (step: OnboardingStep, index: number) => (
    <View key={step.id} style={[styles.stepContainer, { width: screenWidth }]}>
      <View style={styles.stepContent}>
        <View style={[styles.iconContainer, { backgroundColor: step.color }]}>
          <Ionicons name={step.icon as any} size={60} color={colors.text.white} />
        </View>
        
        <Text style={styles.stepTitle}>{step.title}</Text>
        <Text style={styles.stepDescription}>{step.description}</Text>
      </View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {onboardingSteps.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            index === currentStep && styles.paginationDotActive,
          ]}
        />
      ))}
    </View>
  );

  const currentStepData = onboardingSteps[currentStep];
  const isLastStep = currentStep === onboardingSteps.length - 1;
  const isFirstStep = currentStep === 0;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity style={styles.skipButton} onPress={skipOnboarding}>
          <Text style={styles.skipButtonText}>Passer</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        contentOffset={{ x: currentStep * screenWidth, y: 0 }}
        style={styles.stepsContainer}
      >
        {onboardingSteps.map((step, index) => renderStep(step, index))}
      </ScrollView>

      {renderPagination()}

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.navButton, styles.backButton, isFirstStep && styles.navButtonDisabled]}
          onPress={previousStep}
          disabled={isFirstStep}
        >
          <Ionicons 
            name="chevron-back" 
            size={20} 
            color={isFirstStep ? colors.text.light : colors.text.secondary} 
          />
          <Text style={[
            styles.navButtonText,
            isFirstStep && styles.navButtonTextDisabled
          ]}>
            Retour
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, styles.nextButton]}
          onPress={nextStep}
        >
          <Text style={styles.nextButtonText}>
            {isLastStep ? 'Commencer' : 'Suivant'}
          </Text>
          <Ionicons 
            name={isLastStep ? 'checkmark' : 'chevron-forward'} 
            size={20} 
            color={colors.text.white} 
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  skipButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  skipButtonText: {
    fontSize: 16,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  stepsContainer: {
    flex: 1,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  stepContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xl,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  stepDescription: {
    fontSize: 18,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 26,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.border,
    marginHorizontal: spacing.xs,
  },
  paginationDotActive: {
    backgroundColor: colors.primary,
    width: 24,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    minWidth: 100,
    justifyContent: 'center',
  },
  backButton: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  nextButton: {
    backgroundColor: colors.primary,
  },
  navButtonDisabled: {
    backgroundColor: colors.background,
    borderColor: colors.border,
  },
  navButtonText: {
    fontSize: 16,
    color: colors.text.secondary,
    fontWeight: '600',
    marginRight: spacing.sm,
  },
  navButtonTextDisabled: {
    color: colors.text.light,
  },
  nextButtonText: {
    fontSize: 16,
    color: colors.text.white,
    fontWeight: '600',
    marginRight: spacing.sm,
  },
});

export default OnboardingScreen;
