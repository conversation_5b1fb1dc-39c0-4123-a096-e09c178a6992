import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Image,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Meal } from '../types';

const { width: screenWidth } = Dimensions.get('window');

interface RecipeDetailScreenProps {
  route: {
    params: {
      meal: Meal;
    };
  };
  navigation: any;
}

const RecipeDetailScreen: React.FC<RecipeDetailScreenProps> = ({
  route,
  navigation,
}) => {
  const { meal } = route.params;
  const [activeTab, setActiveTab] = useState<'ingredients' | 'instructions'>('ingredients');

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Facile':
        return '#4CAF50';
      case 'Moyen':
        return '#FF9800';
      case 'Difficile':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const renderIngredients = () => (
    <View style={styles.tabContent}>
      {meal.recipe.ingredients.map((ingredient, index) => (
        <View key={ingredient.id} style={styles.ingredientItem}>
          <View style={styles.ingredientBullet} />
          <Text style={styles.ingredientText}>
            <Text style={styles.ingredientQuantity}>
              {ingredient.quantity} {ingredient.unit}
            </Text>
            {' '}
            <Text style={styles.ingredientName}>
              {ingredient.name}
            </Text>
            {ingredient.optional && (
              <Text style={styles.optionalText}> (optionnel)</Text>
            )}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderInstructions = () => (
    <View style={styles.tabContent}>
      {meal.recipe.instructions.map((instruction, index) => (
        <View key={instruction.id} style={styles.instructionItem}>
          <View style={styles.stepNumber}>
            <Text style={styles.stepNumberText}>{instruction.step}</Text>
          </View>
          <View style={styles.instructionContent}>
            <Text style={styles.instructionText}>
              {instruction.description}
            </Text>
            {instruction.duration && (
              <Text style={styles.instructionDuration}>
                ⏱️ {instruction.duration} min
              </Text>
            )}
          </View>
        </View>
      ))}
      
      {meal.recipe.tips && meal.recipe.tips.length > 0 && (
        <View style={styles.tipsContainer}>
          <Text style={styles.tipsTitle}>💡 Conseils</Text>
          {meal.recipe.tips.map((tip, index) => (
            <Text key={index} style={styles.tipText}>
              • {tip}
            </Text>
          ))}
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Header with image */}
      <View style={styles.imageContainer}>
        <Image source={{ uri: meal.image }} style={styles.image} />
        <View style={styles.imageOverlay} />
        
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <View style={styles.imageContent}>
          <Text style={styles.mealTitle}>{meal.name}</Text>
          <Text style={styles.mealCuisine}>{meal.cuisine}</Text>
          
          <View style={styles.mealStats}>
            <View style={styles.statItem}>
              <Ionicons name="time-outline" size={16} color="#fff" />
              <Text style={styles.statText}>
                {meal.prepTime + meal.cookTime} min
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Ionicons name="restaurant-outline" size={16} color="#fff" />
              <Text style={styles.statText}>{meal.servings} pers.</Text>
            </View>
            
            <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(meal.difficulty) }]}>
              <Text style={styles.difficultyText}>{meal.difficulty}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.description}>{meal.description}</Text>
        
        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'ingredients' && styles.activeTab]}
            onPress={() => setActiveTab('ingredients')}
          >
            <Text style={[styles.tabText, activeTab === 'ingredients' && styles.activeTabText]}>
              Ingrédients
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tab, activeTab === 'instructions' && styles.activeTab]}
            onPress={() => setActiveTab('instructions')}
          >
            <Text style={[styles.tabText, activeTab === 'instructions' && styles.activeTabText]}>
              Préparation
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {activeTab === 'ingredients' ? renderIngredients() : renderInstructions()}
        </ScrollView>

        {/* Cooking Mode Button */}
        <TouchableOpacity
          style={styles.cookingModeButton}
          onPress={() => navigation.navigate('CookingMode', { meal })}
        >
          <Ionicons name="play-circle" size={24} color="#fff" />
          <Text style={styles.cookingModeButtonText}>Mode cuisson guidé</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  imageContainer: {
    height: 300,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContent: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
  mealTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  mealCuisine: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 15,
  },
  mealStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  statText: {
    marginLeft: 5,
    fontSize: 14,
    color: '#fff',
  },
  difficultyBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
    marginLeft: 'auto',
  },
  difficultyText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
    marginBottom: 20,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: '#eee',
  },
  activeTab: {
    borderBottomColor: '#FF6B35',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  scrollContent: {
    flex: 1,
  },
  tabContent: {
    paddingBottom: 20,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  ingredientBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF6B35',
    marginTop: 8,
    marginRight: 12,
  },
  ingredientText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 22,
  },
  ingredientQuantity: {
    fontWeight: '600',
    color: '#333',
  },
  ingredientName: {
    color: '#666',
  },
  optionalText: {
    color: '#999',
    fontStyle: 'italic',
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  stepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    marginTop: 2,
  },
  stepNumberText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  instructionContent: {
    flex: 1,
  },
  instructionText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
    marginBottom: 5,
  },
  instructionDuration: {
    fontSize: 14,
    color: '#666',
  },
  tipsContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 5,
  },
  cookingModeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF6B35',
    marginHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 15,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  cookingModeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default RecipeDetailScreen;
