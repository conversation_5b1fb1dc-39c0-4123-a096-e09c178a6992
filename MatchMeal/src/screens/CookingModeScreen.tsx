import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Meal } from '../types';
import { colors, spacing, borderRadius } from '../utils/theme';

interface CookingModeScreenProps {
  route: {
    params: {
      meal: Meal;
    };
  };
  navigation: any;
}

const CookingModeScreen: React.FC<CookingModeScreenProps> = ({
  route,
  navigation,
}) => {
  const { meal } = route.params;
  const [currentStep, setCurrentStep] = useState(0);
  const [timer, setTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<boolean[]>(
    new Array(meal.recipe.instructions.length).fill(false)
  );

  const currentInstruction = meal.recipe.instructions[currentStep];
  const isLastStep = currentStep === meal.recipe.instructions.length - 1;
  const isFirstStep = currentStep === 0;

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning && timer > 0) {
      interval = setInterval(() => {
        setTimer(timer => {
          if (timer <= 1) {
            setIsTimerRunning(false);
            Alert.alert('Temps écoulé !', 'L\'étape est terminée.', [
              { text: 'OK', onPress: () => {} }
            ]);
            return 0;
          }
          return timer - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, timer]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startTimer = (duration: number) => {
    setTimer(duration * 60); // Convert minutes to seconds
    setIsTimerRunning(true);
  };

  const stopTimer = () => {
    setIsTimerRunning(false);
    setTimer(0);
  };

  const toggleTimer = () => {
    setIsTimerRunning(!isTimerRunning);
  };

  const markStepComplete = () => {
    const newCompletedSteps = [...completedSteps];
    newCompletedSteps[currentStep] = true;
    setCompletedSteps(newCompletedSteps);
    
    if (!isLastStep) {
      setCurrentStep(currentStep + 1);
      stopTimer();
    } else {
      Alert.alert(
        'Félicitations !',
        'Vous avez terminé la recette ! Bon appétit !',
        [
          { text: 'Terminer', onPress: () => navigation.goBack() }
        ]
      );
    }
  };

  const goToPreviousStep = () => {
    if (!isFirstStep) {
      setCurrentStep(currentStep - 1);
      stopTimer();
    }
  };

  const goToNextStep = () => {
    if (!isLastStep) {
      setCurrentStep(currentStep + 1);
      stopTimer();
    }
  };

  const exitCookingMode = () => {
    Alert.alert(
      'Quitter le mode cuisson',
      'Êtes-vous sûr de vouloir quitter ? Votre progression sera perdue.',
      [
        { text: 'Continuer à cuisiner', style: 'cancel' },
        { text: 'Quitter', style: 'destructive', onPress: () => navigation.goBack() }
      ]
    );
  };

  const renderProgressBar = () => {
    const progress = ((currentStep + 1) / meal.recipe.instructions.length) * 100;
    
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressText}>
          Étape {currentStep + 1} sur {meal.recipe.instructions.length}
        </Text>
      </View>
    );
  };

  const renderTimer = () => {
    if (!currentInstruction.duration) return null;

    return (
      <View style={styles.timerContainer}>
        <Text style={styles.timerLabel}>Minuteur pour cette étape</Text>
        <View style={styles.timerDisplay}>
          <Text style={styles.timerTime}>{formatTime(timer)}</Text>
          <View style={styles.timerControls}>
            {timer === 0 ? (
              <TouchableOpacity
                style={styles.timerButton}
                onPress={() => startTimer(currentInstruction.duration!)}
              >
                <Ionicons name="play" size={20} color={colors.text.white} />
                <Text style={styles.timerButtonText}>
                  {currentInstruction.duration} min
                </Text>
              </TouchableOpacity>
            ) : (
              <>
                <TouchableOpacity
                  style={[styles.timerButton, styles.timerButtonSecondary]}
                  onPress={toggleTimer}
                >
                  <Ionicons 
                    name={isTimerRunning ? "pause" : "play"} 
                    size={20} 
                    color={colors.primary} 
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.timerButton, styles.timerButtonDanger]}
                  onPress={stopTimer}
                >
                  <Ionicons name="stop" size={20} color={colors.text.white} />
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </View>
    );
  };

  const renderStepsList = () => (
    <View style={styles.stepsOverview}>
      <Text style={styles.stepsTitle}>Aperçu des étapes</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {meal.recipe.instructions.map((instruction, index) => (
          <TouchableOpacity
            key={instruction.id}
            style={[
              styles.stepPreview,
              index === currentStep && styles.stepPreviewActive,
              completedSteps[index] && styles.stepPreviewCompleted,
            ]}
            onPress={() => {
              setCurrentStep(index);
              stopTimer();
            }}
          >
            <View style={styles.stepPreviewNumber}>
              <Text style={[
                styles.stepPreviewNumberText,
                index === currentStep && styles.stepPreviewActiveText,
                completedSteps[index] && styles.stepPreviewCompletedText,
              ]}>
                {completedSteps[index] ? '✓' : index + 1}
              </Text>
            </View>
            <Text style={[
              styles.stepPreviewText,
              index === currentStep && styles.stepPreviewActiveText,
            ]} numberOfLines={2}>
              {instruction.description}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      
      <View style={styles.header}>
        <TouchableOpacity style={styles.exitButton} onPress={exitCookingMode}>
          <Ionicons name="close" size={24} color={colors.text.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{meal.name}</Text>
        <View style={styles.headerRight} />
      </View>

      {renderProgressBar()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.stepContainer}>
          <View style={styles.stepHeader}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>{currentStep + 1}</Text>
            </View>
            <Text style={styles.stepTitle}>Étape {currentStep + 1}</Text>
          </View>

          <Text style={styles.stepDescription}>
            {currentInstruction.description}
          </Text>

          {renderTimer()}
        </View>

        {renderStepsList()}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.navButton, isFirstStep && styles.navButtonDisabled]}
          onPress={goToPreviousStep}
          disabled={isFirstStep}
        >
          <Ionicons 
            name="chevron-back" 
            size={20} 
            color={isFirstStep ? colors.text.light : colors.text.white} 
          />
          <Text style={[
            styles.navButtonText,
            isFirstStep && styles.navButtonTextDisabled
          ]}>
            Précédent
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.completeButton}
          onPress={markStepComplete}
        >
          <Ionicons name="checkmark" size={20} color={colors.text.white} />
          <Text style={styles.completeButtonText}>
            {isLastStep ? 'Terminer' : 'Étape suivante'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, isLastStep && styles.navButtonDisabled]}
          onPress={goToNextStep}
          disabled={isLastStep}
        >
          <Text style={[
            styles.navButtonText,
            isLastStep && styles.navButtonTextDisabled
          ]}>
            Suivant
          </Text>
          <Ionicons 
            name="chevron-forward" 
            size={20} 
            color={isLastStep ? colors.text.light : colors.text.white} 
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.primary,
  },
  exitButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.white,
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  progressBar: {
    height: 6,
    backgroundColor: colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
  },
  progressText: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    padding: spacing.lg,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  stepNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  stepNumberText: {
    color: colors.text.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  stepDescription: {
    fontSize: 18,
    lineHeight: 26,
    color: colors.text.primary,
    marginBottom: spacing.xl,
  },
  timerContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  timerLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  timerDisplay: {
    alignItems: 'center',
  },
  timerTime: {
    fontSize: 48,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  timerControls: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  timerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
  },
  timerButtonSecondary: {
    backgroundColor: colors.surface,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  timerButtonDanger: {
    backgroundColor: colors.danger,
  },
  timerButtonText: {
    color: colors.text.white,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  stepsOverview: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  stepsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  stepPreview: {
    width: 120,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginRight: spacing.md,
    borderWidth: 2,
    borderColor: colors.border,
  },
  stepPreviewActive: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  stepPreviewCompleted: {
    borderColor: colors.success,
    backgroundColor: colors.surface,
  },
  stepPreviewNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  stepPreviewNumberText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.text.secondary,
  },
  stepPreviewActiveText: {
    color: colors.primary,
  },
  stepPreviewCompletedText: {
    color: colors.success,
  },
  stepPreviewText: {
    fontSize: 12,
    color: colors.text.secondary,
    lineHeight: 16,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.text.secondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    flex: 1,
    justifyContent: 'center',
  },
  navButtonDisabled: {
    backgroundColor: colors.border,
  },
  navButtonText: {
    color: colors.text.white,
    fontWeight: '600',
    marginHorizontal: spacing.xs,
  },
  navButtonTextDisabled: {
    color: colors.text.light,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.success,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    marginHorizontal: spacing.md,
  },
  completeButtonText: {
    color: colors.text.white,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
});

export default CookingModeScreen;
