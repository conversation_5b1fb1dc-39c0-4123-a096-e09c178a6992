import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { UserPreferences } from '../types';
import { colors, spacing, borderRadius } from '../utils/theme';
import { mealsData } from '../data/meals';

interface ProfileScreenProps {
  navigation: any;
  userPreferences: UserPreferences;
  onUpdatePreferences: (preferences: UserPreferences) => void;
}

const ProfileScreen: React.FC<ProfileScreenProps> = ({
  navigation,
  userPreferences,
  onUpdatePreferences,
}) => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);

  const getStats = () => {
    const totalMealsDiscovered = userPreferences.likedMeals.length + userPreferences.dislikedMeals.length;
    const likedMeals = userPreferences.likedMeals.length;
    const likeRatio = totalMealsDiscovered > 0 ? (likedMeals / totalMealsDiscovered) * 100 : 0;
    
    const cuisineStats: { [key: string]: number } = {};
    userPreferences.likedMeals.forEach(mealId => {
      const meal = mealsData.find(m => m.id === mealId);
      if (meal) {
        cuisineStats[meal.cuisine] = (cuisineStats[meal.cuisine] || 0) + 1;
      }
    });
    
    const favoriteCuisine = Object.entries(cuisineStats).sort(([,a], [,b]) => b - a)[0]?.[0] || 'Aucune';

    return {
      totalMealsDiscovered,
      likedMeals,
      likeRatio: Math.round(likeRatio),
      favoriteCuisine,
    };
  };

  const stats = getStats();

  const clearAllData = () => {
    Alert.alert(
      'Effacer toutes les données',
      'Cette action supprimera tous vos favoris et préférences. Cette action est irréversible.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Effacer',
          style: 'destructive',
          onPress: () => {
            onUpdatePreferences({
              likedMeals: [],
              dislikedMeals: [],
              dietaryRestrictions: [],
              allergies: [],
              preferredCuisines: [],
            });
            Alert.alert('Données effacées', 'Toutes vos données ont été supprimées.');
          },
        },
      ]
    );
  };

  const renderStatCard = (title: string, value: string | number, icon: string, color: string) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statIcon}>
        <Ionicons name={icon as any} size={24} color={color} />
      </View>
      <View style={styles.statContent}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
      </View>
    </View>
  );

  const renderSettingItem = (
    title: string,
    subtitle: string,
    icon: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Ionicons name={icon as any} size={20} color={colors.primary} />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        </View>
      </View>
      {rightComponent || (
        <Ionicons name="chevron-forward" size={20} color={colors.text.light} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Mon Profil</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Statistiques */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Mes statistiques</Text>
          <View style={styles.statsContainer}>
            {renderStatCard(
              'Repas découverts',
              stats.totalMealsDiscovered,
              'restaurant-outline',
              colors.primary
            )}
            {renderStatCard(
              'Repas aimés',
              stats.likedMeals,
              'heart',
              colors.success
            )}
            {renderStatCard(
              'Taux d\'appréciation',
              `${stats.likeRatio}%`,
              'trending-up',
              colors.info
            )}
            {renderStatCard(
              'Cuisine préférée',
              stats.favoriteCuisine,
              'flag',
              colors.warning
            )}
          </View>
        </View>

        {/* Préférences alimentaires */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Préférences alimentaires</Text>
          <View style={styles.settingsContainer}>
            {renderSettingItem(
              'Restrictions alimentaires',
              `${userPreferences.dietaryRestrictions.length} restriction(s)`,
              'leaf-outline',
              () => {
                // Ouvrir un modal pour gérer les restrictions
                Alert.alert('Fonctionnalité à venir', 'Cette fonctionnalité sera bientôt disponible !');
              }
            )}
            {renderSettingItem(
              'Allergies',
              `${userPreferences.allergies.length} allergie(s)`,
              'warning-outline',
              () => {
                Alert.alert('Fonctionnalité à venir', 'Cette fonctionnalité sera bientôt disponible !');
              }
            )}
            {renderSettingItem(
              'Cuisines préférées',
              `${userPreferences.preferredCuisines.length} cuisine(s)`,
              'globe-outline',
              () => {
                Alert.alert('Fonctionnalité à venir', 'Cette fonctionnalité sera bientôt disponible !');
              }
            )}
          </View>
        </View>

        {/* Paramètres */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Paramètres</Text>
          <View style={styles.settingsContainer}>
            {renderSettingItem(
              'Notifications',
              'Recevoir des suggestions de repas',
              'notifications-outline',
              undefined,
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={colors.surface}
              />
            )}
            {renderSettingItem(
              'Mode sombre',
              'Thème sombre pour l\'application',
              'moon-outline',
              undefined,
              <Switch
                value={darkModeEnabled}
                onValueChange={setDarkModeEnabled}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={colors.surface}
              />
            )}
            {renderSettingItem(
              'Planificateur de repas',
              'Gérer votre planning hebdomadaire',
              'calendar-outline',
              () => navigation.navigate('MealPlanner')
            )}
          </View>
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actions</Text>
          <View style={styles.settingsContainer}>
            {renderSettingItem(
              'Exporter mes données',
              'Sauvegarder mes préférences',
              'download-outline',
              () => {
                Alert.alert('Fonctionnalité à venir', 'Cette fonctionnalité sera bientôt disponible !');
              }
            )}
            {renderSettingItem(
              'Partager l\'application',
              'Recommander MatchMeal à vos amis',
              'share-outline',
              () => {
                Alert.alert('Fonctionnalité à venir', 'Cette fonctionnalité sera bientôt disponible !');
              }
            )}
            {renderSettingItem(
              'Aide et support',
              'FAQ et contact',
              'help-circle-outline',
              () => {
                Alert.alert('Aide', 'Pour toute question, contactez-nous à <EMAIL>');
              }
            )}
          </View>
        </View>

        {/* Zone dangereuse */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.danger }]}>Zone dangereuse</Text>
          <View style={styles.settingsContainer}>
            <TouchableOpacity style={styles.dangerButton} onPress={clearAllData}>
              <Ionicons name="trash-outline" size={20} color={colors.danger} />
              <Text style={styles.dangerButtonText}>Effacer toutes les données</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>MatchMeal v1.0.0</Text>
          <Text style={styles.footerText}>Fait avec ❤️ pour les gourmets</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text.primary,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  section: {
    marginVertical: spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.md,
    marginHorizontal: spacing.lg,
  },
  statsContainer: {
    paddingHorizontal: spacing.lg,
  },
  statCard: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderLeftWidth: 4,
    alignItems: 'center',
  },
  statIcon: {
    marginRight: spacing.md,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  statTitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginTop: 2,
  },
  settingsContainer: {
    backgroundColor: colors.surface,
    marginHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
  },
  settingSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginTop: 2,
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
  },
  dangerButtonText: {
    fontSize: 16,
    color: colors.danger,
    fontWeight: '500',
    marginLeft: spacing.sm,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  footerText: {
    fontSize: 14,
    color: colors.text.light,
    marginBottom: spacing.xs,
  },
});

export default ProfileScreen;
