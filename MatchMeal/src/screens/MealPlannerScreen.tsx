import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Meal, UserPreferences } from '../types';
import { getMealById } from '../data/meals';
import { colors, spacing, borderRadius } from '../utils/theme';

interface MealPlan {
  [date: string]: {
    breakfast?: string;
    lunch?: string;
    dinner?: string;
  };
}

interface MealPlannerScreenProps {
  navigation: any;
  userPreferences: UserPreferences;
  onUpdatePreferences: (preferences: UserPreferences) => void;
}

const MealPlannerScreen: React.FC<MealPlannerScreenProps> = ({
  navigation,
  userPreferences,
  onUpdatePreferences,
}) => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [mealPlan, setMealPlan] = useState<MealPlan>({});
  const [selectedMealType, setSelectedMealType] = useState<'breakfast' | 'lunch' | 'dinner' | null>(null);

  const likedMeals = userPreferences.likedMeals
    .map(id => getMealById(id))
    .filter(meal => meal !== undefined) as Meal[];

  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  const getWeekDates = (startDate: Date): Date[] => {
    const dates = [];
    const start = new Date(startDate);
    start.setDate(start.getDate() - start.getDay()); // Start from Sunday
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(start);
      date.setDate(start.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  const weekDates = getWeekDates(selectedDate);
  const dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];

  const addMealToPlan = (mealId: string, date: string, mealType: 'breakfast' | 'lunch' | 'dinner') => {
    setMealPlan(prev => ({
      ...prev,
      [date]: {
        ...prev[date],
        [mealType]: mealId,
      },
    }));
  };

  const removeMealFromPlan = (date: string, mealType: 'breakfast' | 'lunch' | 'dinner') => {
    setMealPlan(prev => {
      const newPlan = { ...prev };
      if (newPlan[date]) {
        delete newPlan[date][mealType];
        if (Object.keys(newPlan[date]).length === 0) {
          delete newPlan[date];
        }
      }
      return newPlan;
    });
  };

  const getMealTypeIcon = (mealType: 'breakfast' | 'lunch' | 'dinner') => {
    switch (mealType) {
      case 'breakfast':
        return 'sunny-outline';
      case 'lunch':
        return 'partly-sunny-outline';
      case 'dinner':
        return 'moon-outline';
    }
  };

  const getMealTypeName = (mealType: 'breakfast' | 'lunch' | 'dinner') => {
    switch (mealType) {
      case 'breakfast':
        return 'Petit-déjeuner';
      case 'lunch':
        return 'Déjeuner';
      case 'dinner':
        return 'Dîner';
    }
  };

  const renderCalendarHeader = () => (
    <View style={styles.calendarHeader}>
      <TouchableOpacity
        onPress={() => {
          const newDate = new Date(selectedDate);
          newDate.setDate(newDate.getDate() - 7);
          setSelectedDate(newDate);
        }}
      >
        <Ionicons name="chevron-back" size={24} color={colors.text.primary} />
      </TouchableOpacity>
      
      <Text style={styles.monthYear}>
        {selectedDate.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })}
      </Text>
      
      <TouchableOpacity
        onPress={() => {
          const newDate = new Date(selectedDate);
          newDate.setDate(newDate.getDate() + 7);
          setSelectedDate(newDate);
        }}
      >
        <Ionicons name="chevron-forward" size={24} color={colors.text.primary} />
      </TouchableOpacity>
    </View>
  );

  const renderDayColumn = (date: Date, dayName: string) => {
    const dateStr = formatDate(date);
    const dayPlan = mealPlan[dateStr] || {};
    const isToday = formatDate(new Date()) === dateStr;

    return (
      <View key={dateStr} style={styles.dayColumn}>
        <View style={[styles.dayHeader, isToday && styles.todayHeader]}>
          <Text style={[styles.dayName, isToday && styles.todayText]}>{dayName}</Text>
          <Text style={[styles.dayNumber, isToday && styles.todayText]}>
            {date.getDate()}
          </Text>
        </View>

        {(['breakfast', 'lunch', 'dinner'] as const).map(mealType => {
          const mealId = dayPlan[mealType];
          const meal = mealId ? getMealById(mealId) : null;

          return (
            <TouchableOpacity
              key={mealType}
              style={styles.mealSlot}
              onPress={() => {
                if (meal) {
                  Alert.alert(
                    meal.name,
                    'Que voulez-vous faire ?',
                    [
                      { text: 'Voir la recette', onPress: () => navigation.navigate('RecipeDetail', { meal }) },
                      { text: 'Retirer du planning', onPress: () => removeMealFromPlan(dateStr, mealType), style: 'destructive' },
                      { text: 'Annuler', style: 'cancel' },
                    ]
                  );
                } else {
                  setSelectedMealType(mealType);
                  // Ici on pourrait ouvrir un modal pour sélectionner un repas
                  if (likedMeals.length > 0) {
                    const randomMeal = likedMeals[Math.floor(Math.random() * likedMeals.length)];
                    addMealToPlan(randomMeal.id, dateStr, mealType);
                  } else {
                    Alert.alert('Aucun repas favori', 'Ajoutez des repas à vos favoris pour les planifier !');
                  }
                }
              }}
            >
              <Ionicons 
                name={getMealTypeIcon(mealType)} 
                size={16} 
                color={meal ? colors.primary : colors.text.light} 
              />
              {meal ? (
                <Text style={styles.mealName} numberOfLines={2}>
                  {meal.name}
                </Text>
              ) : (
                <Text style={styles.addMealText}>+</Text>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const generateShoppingList = () => {
    const ingredients: { [key: string]: { quantity: number; unit: string } } = {};
    
    Object.values(mealPlan).forEach(dayPlan => {
      Object.values(dayPlan).forEach(mealId => {
        if (mealId) {
          const meal = getMealById(mealId);
          if (meal) {
            meal.recipe.ingredients.forEach(ingredient => {
              const key = ingredient.name.toLowerCase();
              if (ingredients[key]) {
                ingredients[key].quantity += ingredient.quantity;
              } else {
                ingredients[key] = {
                  quantity: ingredient.quantity,
                  unit: ingredient.unit,
                };
              }
            });
          }
        }
      });
    });

    const shoppingList = Object.entries(ingredients).map(([name, { quantity, unit }]) => ({
      name,
      quantity,
      unit,
    }));

    if (shoppingList.length > 0) {
      navigation.navigate('ShoppingList', { shoppingList });
    } else {
      Alert.alert('Planning vide', 'Ajoutez des repas à votre planning pour générer une liste de courses !');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Planning des repas</Text>
        <TouchableOpacity style={styles.shoppingButton} onPress={generateShoppingList}>
          <Ionicons name="list-outline" size={20} color={colors.text.white} />
          <Text style={styles.shoppingButtonText}>Liste de courses</Text>
        </TouchableOpacity>
      </View>

      {renderCalendarHeader()}

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.calendar}>
        <View style={styles.weekContainer}>
          {weekDates.map((date, index) => renderDayColumn(date, dayNames[index]))}
        </View>
      </ScrollView>

      <View style={styles.legend}>
        <Text style={styles.legendTitle}>Légende</Text>
        <View style={styles.legendItems}>
          {(['breakfast', 'lunch', 'dinner'] as const).map(mealType => (
            <View key={mealType} style={styles.legendItem}>
              <Ionicons name={getMealTypeIcon(mealType)} size={16} color={colors.text.secondary} />
              <Text style={styles.legendText}>{getMealTypeName(mealType)}</Text>
            </View>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  shoppingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
  },
  shoppingButtonText: {
    color: colors.text.white,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  monthYear: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    textTransform: 'capitalize',
  },
  calendar: {
    flex: 1,
  },
  weekContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.md,
  },
  dayColumn: {
    width: 120,
    marginHorizontal: spacing.xs,
  },
  dayHeader: {
    alignItems: 'center',
    paddingVertical: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: colors.surface,
  },
  todayHeader: {
    backgroundColor: colors.primary,
  },
  dayName: {
    fontSize: 12,
    color: colors.text.secondary,
    fontWeight: '600',
  },
  dayNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginTop: 2,
  },
  todayText: {
    color: colors.text.white,
  },
  mealSlot: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    minHeight: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  mealName: {
    fontSize: 12,
    color: colors.text.primary,
    textAlign: 'center',
    marginTop: spacing.xs,
    fontWeight: '500',
  },
  addMealText: {
    fontSize: 24,
    color: colors.text.light,
    marginTop: spacing.xs,
  },
  legend: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  legendTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  legendItems: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },
});

export default MealPlannerScreen;
