import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

import DiscoverScreen from '../screens/DiscoverScreen';
import FavoritesScreen from '../screens/FavoritesScreen';
import RecipeDetailScreen from '../screens/RecipeDetailScreen';
import MealPlannerScreen from '../screens/MealPlannerScreen';
import ShoppingListScreen from '../screens/ShoppingListScreen';
import ProfileScreen from '../screens/ProfileScreen';
import CookingModeScreen from '../screens/CookingModeScreen';
import SearchScreen from '../screens/SearchScreen';
import { UserPreferences } from '../types';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

interface AppNavigatorProps {
  userPreferences: UserPreferences;
  onUpdatePreferences: (preferences: UserPreferences) => void;
}

const MainTabs: React.FC<AppNavigatorProps> = ({ userPreferences, onUpdatePreferences }) => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Discover') {
            iconName = focused ? 'restaurant' : 'restaurant-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'Search') {
            iconName = focused ? 'search' : 'search-outline';
          } else if (route.name === 'MealPlanner') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: '#666',
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 1,
          borderTopColor: '#eee',
          paddingTop: 5,
          paddingBottom: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Discover" 
        options={{ tabBarLabel: 'Découvrir' }}
      >
        {(props) => (
          <DiscoverScreen
            {...props}
            userPreferences={userPreferences}
            onUpdatePreferences={onUpdatePreferences}
          />
        )}
      </Tab.Screen>
      
      <Tab.Screen
        name="Search"
        options={{ tabBarLabel: 'Recherche' }}
      >
        {(props) => (
          <SearchScreen
            {...props}
            userPreferences={userPreferences}
            onUpdatePreferences={onUpdatePreferences}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Favorites"
        options={{ tabBarLabel: 'Favoris' }}
      >
        {(props) => (
          <FavoritesScreen
            {...props}
            userPreferences={userPreferences}
            onUpdatePreferences={onUpdatePreferences}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="MealPlanner"
        options={{ tabBarLabel: 'Planning' }}
      >
        {(props) => (
          <MealPlannerScreen
            {...props}
            userPreferences={userPreferences}
            onUpdatePreferences={onUpdatePreferences}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Profile"
        options={{ tabBarLabel: 'Profil' }}
      >
        {(props) => (
          <ProfileScreen
            {...props}
            userPreferences={userPreferences}
            onUpdatePreferences={onUpdatePreferences}
          />
        )}
      </Tab.Screen>
    </Tab.Navigator>
  );
};

const AppNavigator: React.FC<AppNavigatorProps> = ({ userPreferences, onUpdatePreferences }) => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="MainTabs">
          {(props) => (
            <MainTabs
              {...props}
              userPreferences={userPreferences}
              onUpdatePreferences={onUpdatePreferences}
            />
          )}
        </Stack.Screen>
        
        <Stack.Screen
          name="RecipeDetail"
          component={RecipeDetailScreen}
          options={{
            presentation: 'modal',
            gestureEnabled: true,
            gestureDirection: 'vertical',
          }}
        />

        <Stack.Screen
          name="ShoppingList"
          component={ShoppingListScreen}
          options={{
            presentation: 'modal',
            gestureEnabled: true,
            gestureDirection: 'vertical',
          }}
        />

        <Stack.Screen
          name="CookingMode"
          component={CookingModeScreen}
          options={{
            presentation: 'fullScreenModal',
            gestureEnabled: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
