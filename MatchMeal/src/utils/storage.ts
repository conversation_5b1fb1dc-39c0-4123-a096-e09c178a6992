import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserPreferences } from '../types';

const PREFERENCES_KEY = 'user_preferences';

const defaultPreferences: UserPreferences = {
  likedMeals: [],
  dislikedMeals: [],
  dietaryRestrictions: [],
  allergies: [],
  preferredCuisines: [],
};

export const saveUserPreferences = async (preferences: UserPreferences): Promise<void> => {
  try {
    const jsonValue = JSON.stringify(preferences);
    await AsyncStorage.setItem(PREFERENCES_KEY, jsonValue);
  } catch (error) {
    console.error('Error saving user preferences:', error);
  }
};

export const loadUserPreferences = async (): Promise<UserPreferences> => {
  try {
    const jsonValue = await AsyncStorage.getItem(PREFERENCES_KEY);
    if (jsonValue != null) {
      return JSON.parse(jsonValue);
    }
    return defaultPreferences;
  } catch (error) {
    console.error('Error loading user preferences:', error);
    return defaultPreferences;
  }
};

export const clearUserPreferences = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(PREFERENCES_KEY);
  } catch (error) {
    console.error('Error clearing user preferences:', error);
  }
};
